# ATLAS Desktop Application Guide

## Overview
ATLAS is now available as a standalone desktop application with multiple UI options.

## Quick Start

### Windows Users
1. **Easiest Method**: Double-click `ATLAS.pyw`
   - This will automatically launch the best available desktop interface
   - No console window will appear

2. **Alternative Methods**:
   - Double-click `ATLAS_Desktop.bat` for the Tkinter version
   - Run `python atlas_pro.py` for the professional PyQt6 version
   - Run `python atlas_app.py` for the standard Tkinter version

### Mac/Linux Users
```bash
# Make the launcher executable
chmod +x ATLAS.pyw

# Run the desktop app
./ATLAS.pyw

# Or run directly
python atlas_pro.py  # Professional version
python atlas_app.py  # Standard version
```

## Available Desktop Applications

### 1. **ATLAS Professional (atlas_pro.py)**
- Modern PyQt6-based interface
- Tabbed interface with Cha<PERSON> and Dashboard
- Professional styling and animations
- Embedded web view for full dashboard
- Real-time status updates

### 2. **ATLAS Standard (atlas_app.py)**
- Tkinter-based interface (works everywhere)
- Three tabs: Chat, Positions, Account
- Quick action buttons
- Live position monitoring
- Clean, simple interface

### 3. **ATLAS Desktop (atlas_desktop.py)**
- PyWebView-based application
- Runs the web interface in a native window
- Minimal UI, maximum compatibility

## Features

### Chat Interface
- Natural language trading commands
- Quick action buttons:
  - 🚀 Make me $50
  - 📊 Show Positions
  - 💰 Account Info
  - 📈 Market Status
- Real-time responses
- Color-coded messages

### Live Positions Tab
- Real-time P&L tracking
- Color-coded profit/loss
- One-click position exit
- Automatic refresh
- Position summary

### Account Tab
- Portfolio value
- Buying power
- Cash balance
- Day trade counter
- Total P&L tracking

## Installation

### First Time Setup
```bash
# Install Python dependencies
pip install -r requirements.txt

# For the professional version, also install:
pip install PyQt6 PyQt6-WebEngine
```

### Creating a Desktop Shortcut (Windows)
1. Right-click on `ATLAS.pyw`
2. Select "Create shortcut"
3. Move shortcut to desktop
4. Optional: Right-click shortcut → Properties → Change Icon

## Troubleshooting

### "Server failed to start"
- Check if port 8080 is already in use
- Ensure all dependencies are installed
- Check firewall settings

### "Module not found" errors
```bash
# Reinstall all dependencies
pip install -r requirements.txt --upgrade
```

### PyQt6 Issues
```bash
# If PyQt6 fails, the app will fall back to Tkinter
# To fix PyQt6:
pip uninstall PyQt6 PyQt6-WebEngine
pip install PyQt6==6.5.3 PyQt6-WebEngine==6.5.0
```

### Performance Issues
- Close unnecessary browser tabs
- Restart the application
- Check system resources

## Advanced Usage

### Running Multiple Instances
Each instance needs a different port:
```python
# Edit main.py to change port
uvicorn.run(app, host="127.0.0.1", port=8081)  # Change port number
```

### Custom Themes
Edit the stylesheet in `atlas_pro.py` or `atlas_app.py` to customize appearance.

### Debugging
Run with console to see logs:
```bash
python atlas_app.py  # Instead of .pyw
```

## Security Notes
- The desktop app runs a local server on port 8080
- Only accessible from your computer (127.0.0.1)
- API keys are stored locally in .env file
- All communication is local (no external servers)

## Updates
To update ATLAS:
1. Pull latest code or download new version
2. Run `pip install -r requirements.txt --upgrade`
3. Restart the desktop application

## Support
- Check logs in `atlas_chat.log` for errors
- Server logs appear in console if run with .py extension
- Create an issue on GitHub for bugs

---

Enjoy trading with ATLAS Desktop! 🚀 