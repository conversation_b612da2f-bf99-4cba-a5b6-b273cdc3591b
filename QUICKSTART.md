# 🚀 ATLAS Quick Start Guide

## 1. Prerequisites

- Python 3.8 or higher
- Windows, Mac, or Linux
- Internet connection

## 2. Installation (5 minutes)

### Step 1: Clone or Download ATLAS
```bash
git clone https://github.com/your-repo/atlas.git
cd atlas
```

### Step 2: Create Virtual Environment
```bash
python -m venv venv

# Windows
venv\Scripts\activate

# Mac/Linux
source venv/bin/activate
```

### Step 3: Install Dependencies
```bash
pip install -r requirements.txt
```

## 3. Configuration (2 minutes)

### Step 1: Run the Launcher
```bash
# Windows
START_ATLAS.bat

# Mac/Linux
python atlas_launcher.py
```

### Step 2: Add Your API Keys
The launcher will create a `.env` file. Edit it with your API keys:

```env
# Get from https://alpaca.markets (free paper trading account)
APCA_API_KEY_ID=your_alpaca_key_here
APCA_API_SECRET_KEY=your_alpaca_secret_here

# Get from https://platform.openai.com
OPENAI_API_KEY=your_openai_key_here
```

## 4. Start Trading! (1 minute)

### Run ATLAS
```bash
# Windows - Double click START_ATLAS.bat
# Or from command line:
python atlas_launcher.py
```

### Your First Commands

Once ATLAS opens in your browser, try these commands:

1. **Check Your Account**
   ```
   "Show me my account status"
   ```

2. **Find Trading Opportunities**
   ```
   "Find the best trades right now"
   ```

3. **Make Money**
   ```
   "Make me $50 today"
   ```

4. **Get Market Info**
   ```
   "What's happening with AAPL?"
   ```

## 5. Common Issues & Solutions

### Issue: "Server won't start"
**Solution**: Check if port 8080 is already in use
```bash
# Windows
netstat -ano | findstr :8080

# Mac/Linux
lsof -i :8080
```

### Issue: "API errors (400)"
**Solution**: Make sure your API keys are correct in `.env`

### Issue: "No module named 'xxx'"
**Solution**: Install missing dependencies
```bash
pip install -r requirements.txt
```

## 6. Safety First!

- **Start with Paper Trading**: Alpaca provides free paper trading accounts
- **Set Risk Limits**: ATLAS uses 2% risk per trade by default
- **Monitor Positions**: Always check your positions before closing
- **Test First**: Try strategies in paper mode before real money

## 7. Next Steps

1. Read the [Full Documentation](README_COMPREHENSIVE.md)
2. Review the [Development Roadmap](ATLAS_ROADMAP.md)
3. Join our Discord community (coming soon)
4. Report issues on GitHub

## Need Help?

- **Documentation**: See README_COMPREHENSIVE.md
- **Roadmap**: See ATLAS_ROADMAP.md
- **Logs**: Check `logs/atlas.log` for errors

---

**Remember**: Trading involves risk. Start small, learn the system, and never trade more than you can afford to lose.

Happy Trading! 🚀 