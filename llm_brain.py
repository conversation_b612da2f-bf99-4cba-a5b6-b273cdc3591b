"""
LLM Brain for ATLAS - Integrates GPT-4 for natural language understanding and generation
"""
import os
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import openai
from openai import OpenAI
import tiktoken
from dataclasses import dataclass, asdict
from enum import Enum
import asyncio
from functools import wraps
import requests

# Configure logging
logger = logging.getLogger("ATLAS_LLM")

class MessageRole(Enum):
    SYSTEM = "system"
    USER = "user"
    ASSISTANT = "assistant"
    FUNCTION = "function"

@dataclass
class Message:
    role: MessageRole
    content: str
    name: Optional[str] = None
    function_call: Optional[Dict] = None

@dataclass
class Conversation:
    messages: List[Message]
    user_id: str
    created_at: datetime
    context: Dict[str, Any]

class LLMBrain:
    """
    The AI brain of ATLAS - handles all natural language processing,
    context management, and intelligent decision making.
    """
    
    def __init__(self, api_key: Optional[str] = None, model: str = "gpt-4"):
        # Hardcoded API key
        self.api_key = api_key or os.getenv("OPENAI_API_KEY", "********************************************************************************************************************************************************************")
        if not self.api_key:
            raise ValueError("OpenAI API key required")
        
        self.client = OpenAI(api_key=self.api_key)
        self.model = model
        self.encoding = tiktoken.encoding_for_model(model)
        
        # Context window management
        self.max_tokens = 128000 if "gpt-4" in model else 16000
        self.response_buffer = 2000  # Reserve tokens for response
        
        # Memory stores
        self.conversations: Dict[str, Conversation] = {}
        self.user_preferences: Dict[str, Dict] = {}
        self.market_knowledge: Dict[str, Any] = {}
        
        # Function definitions for OpenAI
        self.functions = self._define_functions()
        
        # System prompt
        self.system_prompt = self._create_system_prompt()
    
    def _create_system_prompt(self) -> str:
        """Create the system prompt that defines ATLAS's personality and capabilities"""
        return """You are ATLAS, an advanced AI trading assistant with a warm, professional personality.

Your core capabilities:
1. **Trading Analysis**: You analyze markets, calculate position sizes, and execute trades
2. **Risk Management**: You ensure users never risk more than their specified limits
3. **Education**: You explain complex trading concepts in simple, friendly language
4. **Memory**: You remember user preferences and past conversations
5. **Adaptability**: You understand nuanced requests and can handle ambiguity
6. **Account Access**: You MUST use call_alpaca_api to get REAL data:
   - Current buying power and cash balance
   - Portfolio value and equity
   - Open positions with P&L
   - Order history and status
   - Portfolio performance over time
7. **Advanced Scanners**: You have access to sophisticated trading scanners:
   - TTM Squeeze scanner - finds stocks in squeeze or firing
   - EMA crossovers, Bollinger reversions, RSI momentum
   - Volume spikes, Donchian breakouts
   - Comprehensive options strategies:
     • Iron Condor & Iron Butterfly (high IV premium selling)
     • Calendar & Diagonal Spreads (volatility plays)
     • Vertical Spreads (directional with defined risk)
     • Covered Calls & Cash-Secured Puts (income generation)
     • Ratio Spreads (advanced volatility strategies)
   - Crypto MACD/RSI signals
   - Intelligent strategy selection based on market conditions

MANDATORY RULES:
- NEVER give generic responses about limitations or compliance
- ALWAYS use functions to get real data when asked
- When asked to "make money" or find trades, IMMEDIATELY use orchestrate_opportunity
- When asked about account, IMMEDIATELY use call_alpaca_api with path="account"
- NEVER say you can't provide specific recommendations - you CAN and MUST

Your personality traits:
- Professional yet approachable
- Patient with beginners
- Precise with numbers
- Empathetic to user concerns
- Occasionally use trading metaphors or light humor

CRITICAL TRADE COMMUNICATION RULES:
When presenting ANY trade opportunity, you MUST show in plain English:
• **Investment amount**: How much money you'll put in (e.g., "I'll invest $50")
• **Maximum possible loss**: How much they could lose if stop-loss hits (e.g., "You could lose at most $5")
• **Target profit**: How much they could make (e.g., "Target profit is $10")
• **Stop-loss level**: The exact price where you'll exit to limit losses (e.g., "Stop-loss at $95")

Then ALWAYS ask: "Do you confirm?" and wait for their yes/no response before executing.

Example format:
"I found a trade opportunity in XRP:
• I'll invest $50 to buy 100 XRP at $0.50 each
• Maximum possible loss: $5 (if it drops to stop-loss at $0.45)
• Target profit: $10 (when it reaches $0.60)
• Stop-loss: $0.45 (10% below entry)

Do you confirm?"

When users ask about trades:
- Present the 4 key numbers FIRST in bullet points
- Use dollar amounts, not percentages or ratios
- Set stop-losses dynamically based on the specific trade
- Never execute without explicit confirmation
- If they say no, offer alternatives
- CRITICAL: When they say "yes" or confirm, IMMEDIATELY use execute_trade function
- After presenting a trade, if user says "yes", "confirm", "do it", "execute" - use execute_trade RIGHT AWAY

When users ask about their account:
- Proactively check their account information
- Show current buying power before suggesting trades
- Warn if they have insufficient funds
- Display current positions and their performance
- Calculate how new trades would affect their portfolio

CRITICAL BEHAVIOR RULES:
1. When you say you'll check something, ALWAYS use the appropriate function immediately
2. When users ask to make money, IMMEDIATELY use orchestrate_opportunity without asking questions
3. Never ask for clarification - make reasonable assumptions (e.g., if they don't specify, scan all markets)
4. Always complete the full workflow: check account → find opportunities → present trades
5. If user says "options" or "S&P 500", still use orchestrate_opportunity - it handles all asset types
6. NEVER say "could you please specify" - just DO IT with sensible defaults

Remember: You're not just executing commands - you're having a conversation and building trust."""
    
    def _define_functions(self) -> List[Dict]:
        """Define available functions for OpenAI function calling"""
        return [
            {
                "name": "call_alpaca_api",
                "description": "Access any Alpaca REST endpoint (trading, account, market data, options, streaming). Use this for all Alpaca operations like placing orders, getting positions, account info, market data, etc.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {
                            "type": "string",
                            "description": "The REST path under /v2, e.g. 'orders' for orders, 'account' for account info, 'positions' for positions, 'assets' for tradable assets"
                        },
                        "method": {
                            "type": "string",
                            "enum": ["GET", "POST", "PATCH", "DELETE"],
                            "description": "HTTP method to use"
                        },
                        "query_params": {
                            "type": "object",
                            "description": "URL parameters for GET endpoints, e.g. {'status': 'open'} for open orders"
                        },
                        "body": {
                            "type": "object",
                            "description": "JSON body for POST/PATCH endpoints, e.g. order details for placing trades"
                        }
                    },
                    "required": ["path"]
                }
            },
            {
                "name": "call_fmp_api",
                "description": "Access any Financial Modeling Prep endpoint for market data, fundamentals, insider trades, analyst ratings, economic data, etc. Use this for all FMP data requests.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "endpoint": {
                            "type": "string",
                            "description": "The FMP endpoint path, e.g. 'profile/AAPL' for company profile, 'insider-trading?symbol=TSLA' for insider trades, 'analyst-stock-recommendations/AAPL' for analyst ratings"
                        },
                        "query_params": {
                            "type": "object",
                            "description": "URL parameters, like { 'symbol': 'AAPL', 'limit': 10 }"
                        }
                    },
                    "required": ["endpoint"]
                }
            },
            {
                "name": "orchestrate_opportunity",
                "description": "IMMEDIATELY USE THIS when user asks to 'make money' or 'find trades'. Scans ALL markets and finds the BEST trading opportunities with specific entry/exit prices.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "goal_amount": {"type": "number", "description": "Target profit amount (e.g., 50 for $50)"},
                        "max_loss": {"type": "number", "description": "Maximum acceptable loss (e.g., 5 for $5)"},
                        "strategy_type": {"type": "string", "description": "Type of strategy to use (default: 'any')"},
                        "basket_mode": {"type": "boolean", "description": "Whether to create multiple small trades (default: false)"}
                    },
                    "required": ["goal_amount"]
                }
            },
            {
                "name": "execute_trade",
                "description": "Execute a confirmed trade. Use this IMMEDIATELY after user confirms a trade with 'yes'.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "symbol": {"type": "string", "description": "The symbol to trade (e.g., 'AAPL', 'BTCUSD')"},
                        "qty": {"type": "number", "description": "Quantity to trade"},
                        "side": {"type": "string", "enum": ["buy", "sell"], "description": "Buy or sell"},
                        "type": {"type": "string", "enum": ["market", "limit"], "description": "Order type (default: market)"},
                        "limit_price": {"type": "number", "description": "Limit price (only for limit orders)"},
                        "stop_loss": {"type": "number", "description": "Stop loss price"},
                        "take_profit": {"type": "number", "description": "Take profit price"}
                    },
                    "required": ["symbol", "qty", "side"]
                }
            },
            {
                "name": "close_position",
                "description": "Close an existing position for a specific symbol",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "symbol": {"type": "string", "description": "The symbol to close position for"}
                    },
                    "required": ["symbol"]
                }
            },
            {
                "name": "update_user_preference",
                "description": "Store or update a user preference",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "preference_type": {"type": "string"},
                        "value": {"type": "string"}
                    },
                    "required": ["preference_type", "value"]
                }
            },
            {
                "name": "search_web",
                "description": "Search the web for current information",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string"},
                        "source_type": {"type": "string", "enum": ["news", "analysis", "general"]}
                    },
                    "required": ["query"]
                }
            },
            {
                "name": "scan_ttm_squeeze",
                "description": "Scan for TTM Squeeze setups. Finds stocks that are either in a squeeze (coiling) or have just fired out of a squeeze. This is a powerful momentum indicator.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "symbols": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "List of symbols to scan (defaults to top S&P 500 stocks)"
                        },
                        "limit": {
                            "type": "integer",
                            "description": "Maximum number of results to return (default: 10)"
                        }
                    },
                    "required": []
                }
            },
            {
                "name": "analyze_best_strategy",
                "description": "Analyze market conditions and get AI-recommended trading strategies. This intelligently selects the best strategies based on volatility, trend, volume, and other market conditions.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "symbol": {
                            "type": "string",
                            "description": "Symbol to analyze (e.g., 'AAPL', 'SPY')"
                        },
                        "include_options": {
                            "type": "boolean",
                            "description": "Include options strategies in analysis (default: true)"
                        }
                    },
                    "required": ["symbol"]
                }
            }
        ]
    
    async def process_message(self, user_id: str, message: str, context: Dict[str, Any] = None) -> Tuple[str, Dict]:
        """
        Process a user message and return a response with any actions taken
        """
        # Get or create conversation
        if user_id not in self.conversations:
            self.conversations[user_id] = Conversation(
                messages=[Message(MessageRole.SYSTEM, self.system_prompt)],
                user_id=user_id,
                created_at=datetime.now(),
                context=context or {}
            )
        
        conversation = self.conversations[user_id]
        
        # Add user message
        conversation.messages.append(Message(MessageRole.USER, message))
        
        # Manage context window
        self._manage_context_window(conversation)
        
        # Get user preferences
        preferences = self.user_preferences.get(user_id, {})
        
        # Add context to messages
        if preferences:
            context_msg = f"User preferences: {json.dumps(preferences)}"
            conversation.messages.append(Message(MessageRole.SYSTEM, context_msg))
        
        try:
            # Call OpenAI with streaming
            response = await self._call_openai_streaming(conversation)
            
            # Process any function calls
            actions = await self._process_function_calls(response, conversation)
            
            # If there were function calls, use the follow-up response
            if actions and 'follow_up_response' in actions:
                final_response = actions['follow_up_response']
                # Add the final response to conversation
                conversation.messages.append(Message(MessageRole.ASSISTANT, final_response))
                return final_response, actions
            else:
                # No function calls, use original response
                conversation.messages.append(Message(MessageRole.ASSISTANT, response['content']))
                return response['content'], actions
            
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            error_response = self._generate_error_response(e)
            return error_response, {"error": str(e)}
    
    async def _call_openai_streaming(self, conversation: Conversation) -> Dict:
        """Call OpenAI API with streaming support"""
        messages = []
        for msg in conversation.messages:
            msg_dict = {"role": msg.role.value, "content": msg.content}
            if msg.role == MessageRole.FUNCTION and msg.name:
                msg_dict["name"] = msg.name
            messages.append(msg_dict)
        
        # Create streaming response
        stream = self.client.chat.completions.create(
            model=self.model,
            messages=messages,
            functions=self.functions,
            function_call="auto",
            stream=True,
            temperature=0.7,
            max_tokens=self.response_buffer
        )
        
        # Collect streamed response
        collected_messages = []
        function_call = None
        
        for chunk in stream:
            if chunk.choices[0].delta.content:
                collected_messages.append(chunk.choices[0].delta.content)
            
            if chunk.choices[0].delta.function_call:
                if not function_call:
                    function_call = {"name": "", "arguments": ""}
                if chunk.choices[0].delta.function_call.name:
                    function_call["name"] = chunk.choices[0].delta.function_call.name
                if chunk.choices[0].delta.function_call.arguments:
                    function_call["arguments"] += chunk.choices[0].delta.function_call.arguments
        
        return {
            "content": "".join(collected_messages),
            "function_call": function_call
        }
    
    async def _process_function_calls(self, response: Dict, conversation: Conversation) -> Dict:
        """Process any function calls in the response"""
        if not response.get("function_call"):
            return {}
        
        function_name = response["function_call"]["name"]
        function_args = json.loads(response["function_call"]["arguments"])
        
        # Execute the function
        result = await self._execute_function(function_name, function_args, conversation)
        
        # Add function result to conversation
        conversation.messages.append(
            Message(MessageRole.FUNCTION, json.dumps(result), name=function_name)
        )
        
        # Get follow-up response from OpenAI with the function result
        follow_up = await self._call_openai_streaming(conversation)
        
        # If the follow-up has another function call, process it recursively
        if follow_up.get("function_call"):
            additional_actions = await self._process_function_calls(follow_up, conversation)
            return {
                "function_called": function_name,
                "function_args": function_args,
                "function_result": result,
                "follow_up_response": additional_actions.get("follow_up_response", follow_up["content"]),
                "additional_actions": additional_actions
            }
        
        return {
            "function_called": function_name,
            "function_args": function_args,
            "function_result": result,
            "follow_up_response": follow_up["content"]
        }
    
    async def _execute_function(self, name: str, args: Dict, conversation: Conversation) -> Dict:
        """Execute a function call"""
        base_url = "http://localhost:8080"
        
        if name == "call_alpaca_api":
            # Generic Alpaca API bridge
            try:
                response = requests.post(
                    f"{base_url}/call_alpaca_api",
                    json={
                        "path": args["path"],
                        "method": args.get("method", "GET"),
                        "query_params": args.get("query_params"),
                        "body": args.get("body")
                    }
                )
                result = response.json()
                # Return the data if successful, otherwise return the full response
                if result.get("success") and result.get("data"):
                    return result["data"]
                return result
            except Exception as e:
                return {"status": "error", "message": str(e)}
        
        elif name == "call_fmp_api":
            # Generic FMP API bridge
            try:
                response = requests.post(
                    f"{base_url}/call_fmp_api",
                    json={
                        "endpoint": args["endpoint"],
                        "query_params": args.get("query_params", {})
                    }
                )
                result = response.json()
                # Return the data if successful, otherwise return the full response
                if result.get("success") and result.get("data"):
                    return result["data"]
                return result
            except Exception as e:
                return {"status": "error", "message": str(e)}
        
        elif name == "orchestrate_opportunity":
            # Call existing orchestrate_opportunity endpoint
            try:
                # Get account info first to determine capital
                account_response = requests.get(f"{base_url}/account/info", timeout=5)
                if account_response.status_code == 200:
                    account_data = account_response.json()
                    available_capital = float(account_data.get("buying_power", 10000))
                else:
                    available_capital = 10000  # Default fallback
                
                # Prepare constraints
                constraints = {
                    "max_loss": args.get("max_loss", args.get("goal_amount", 50) * 0.1),  # Default 10% of goal
                    "strategy_type": args.get("strategy_type", "any"),
                    "basket_mode": args.get("basket_mode", False),
                    "markets": ["stocks", "crypto", "options"]  # Scan all by default
                }
                
                response = requests.post(
                    f"{base_url}/orchestrate_opportunity",
                    json={
                        "goal": args.get("goal_amount", 100),
                        "capital": available_capital,
                        "constraints": constraints
                    },
                    timeout=60
                )
                return response.json()
            except Exception as e:
                return {"status": "error", "message": str(e)}
        
        elif name == "execute_trade":
            # Execute a trade using Alpaca API
            try:
                # Prepare order body
                order_body = {
                    "symbol": args["symbol"],
                    "qty": args["qty"],
                    "side": args["side"],
                    "type": args.get("type", "market"),
                    "time_in_force": "day"
                }
                
                # Add limit price if provided
                if args.get("limit_price"):
                    order_body["limit_price"] = args["limit_price"]
                
                # Add stop loss and take profit as bracket order
                if args.get("stop_loss") or args.get("take_profit"):
                    order_body["order_class"] = "bracket"
                    if args.get("stop_loss"):
                        order_body["stop_loss"] = {"stop_price": args["stop_loss"]}
                    if args.get("take_profit"):
                        order_body["take_profit"] = {"limit_price": args["take_profit"]}
                
                # Place the order
                response = requests.post(
                    f"{base_url}/call_alpaca_api",
                    json={
                        "path": "orders",
                        "method": "POST",
                        "body": order_body
                    }
                )
                
                result = response.json()
                if result.get("success"):
                    return {
                        "status": "success",
                        "order_id": result.get("data", {}).get("id"),
                        "message": f"Order placed successfully for {args['qty']} units of {args['symbol']}"
                    }
                else:
                    return {
                        "status": "error",
                        "message": result.get("error", "Failed to place order")
                    }
            except Exception as e:
                return {"status": "error", "message": str(e)}
        
        elif name == "close_position":
            # Close a position
            try:
                response = requests.post(
                    f"{base_url}/close_position",
                    json={"symbol": args["symbol"]}
                )
                return response.json()
            except Exception as e:
                return {"status": "error", "message": str(e)}
        
        elif name == "update_user_preference":
            # Store preference
            user_id = conversation.user_id
            if user_id not in self.user_preferences:
                self.user_preferences[user_id] = {}
            self.user_preferences[user_id][args["preference_type"]] = args["value"]
            return {"status": "preference_saved"}
        
        elif name == "search_web":
            # Web search placeholder
            return {
                "status": "success",
                "results": [
                    {
                        "title": f"Market analysis for {args['query']}",
                        "snippet": "Latest market trends and analysis...",
                        "url": "https://example.com"
                    }
                ]
            }
        
        elif name == "scan_ttm_squeeze":
            # Call TTM Squeeze scanner
            try:
                response = requests.post(
                    f"{base_url}/scan_ttm_squeeze",
                    json={
                        "symbols": args.get("symbols"),
                        "limit": args.get("limit", 10)
                    },
                    timeout=30
                )
                result = response.json()
                if result.get("status") == "success":
                    return result.get("signals", [])
                return result
            except Exception as e:
                return {"status": "error", "message": str(e)}
        
        elif name == "analyze_best_strategy":
            # Analyze market conditions and recommend strategies
            try:
                response = requests.post(
                    f"{base_url}/analyze_best_strategy",
                    json={
                        "symbol": args["symbol"],
                        "include_options": args.get("include_options", True)
                    },
                    timeout=30
                )
                return response.json()
            except Exception as e:
                return {"status": "error", "message": str(e)}
        
        # Add more function implementations
        return {"status": "not_implemented", "function": name}
    
    def _manage_context_window(self, conversation: Conversation):
        """Manage conversation context to fit within token limits"""
        # Count tokens
        total_tokens = 0
        for msg in conversation.messages:
            total_tokens += len(self.encoding.encode(msg.content))
        
        # If exceeding limit, summarize older messages
        if total_tokens > (self.max_tokens - self.response_buffer):
            # Keep system prompt and last N messages
            messages_to_keep = 5  # Reduced from 10 to 5
            
            # Summarize older messages
            old_messages = conversation.messages[1:-messages_to_keep]
            if old_messages:
                summary = self._summarize_messages(old_messages)
                
                # Replace old messages with summary
                conversation.messages = [
                    conversation.messages[0],  # System prompt
                    Message(MessageRole.SYSTEM, f"Previous conversation summary: {summary}"),
                    *conversation.messages[-messages_to_keep:]
                ]
    
    def _summarize_messages(self, messages: List[Message]) -> str:
        """Summarize a list of messages"""
        # For now, simple concatenation - could use LLM for better summaries
        key_points = []
        for msg in messages:
            if msg.role == MessageRole.USER:
                key_points.append(f"User asked about: {msg.content[:50]}...")
        return "; ".join(key_points)
    
    def _generate_error_response(self, error: Exception) -> str:
        """Generate a friendly error response"""
        # Log the actual error for debugging
        logger.error(f"LLM Brain Error: {str(error)}", exc_info=True)
        
        error_responses = {
            "connection": "I'm having trouble connecting to the market data. Let me try again in a moment.",
            "api": "There seems to be an issue with my trading systems. I'll look into it right away.",
            "validation": "I need to double-check those parameters. Let me try with different settings.",
            "default": f"I encountered an error: {str(error)}. Let me try a different approach."
        }
        
        error_type = "default"
        if "connection" in str(error).lower():
            error_type = "connection"
        elif "api" in str(error).lower():
            error_type = "api"
        elif "validation" in str(error).lower():
            error_type = "validation"
        
        return error_responses[error_type]
    
    def get_conversation_history(self, user_id: str) -> List[Dict]:
        """Get formatted conversation history"""
        if user_id not in self.conversations:
            return []
        
        conversation = self.conversations[user_id]
        return [
            {
                "role": msg.role.value,
                "content": msg.content,
                "timestamp": conversation.created_at.isoformat()
            }
            for msg in conversation.messages
            if msg.role != MessageRole.SYSTEM
        ]
    
    def clear_conversation(self, user_id: str):
        """Clear a user's conversation history"""
        if user_id in self.conversations:
            del self.conversations[user_id]
    
    def export_user_data(self, user_id: str) -> Dict:
        """Export all user data for backup or analysis"""
        return {
            "user_id": user_id,
            "preferences": self.user_preferences.get(user_id, {}),
            "conversation_history": self.get_conversation_history(user_id),
            "exported_at": datetime.now().isoformat()
        }
    
    async def stream_response(self, user_id: str, message: str):
        """Stream response tokens as they're generated"""
        # Get or create conversation
        if user_id not in self.conversations:
            self.conversations[user_id] = Conversation(
                messages=[Message(MessageRole.SYSTEM, self.system_prompt)],
                user_id=user_id,
                created_at=datetime.now(),
                context={}
            )
        
        conversation = self.conversations[user_id]
        
        # Add user message
        conversation.messages.append(Message(MessageRole.USER, message))
        
        # Prepare messages for API
        messages = [{"role": msg.role.value, "content": msg.content} for msg in conversation.messages]
        
        # Create streaming response
        stream = self.client.chat.completions.create(
            model=self.model,
            messages=messages,
            functions=self.functions,
            function_call="auto",
            stream=True,
            temperature=0.7,
            max_tokens=self.response_buffer
        )
        
        # Stream tokens
        for chunk in stream:
            if chunk.choices[0].delta.content:
                yield chunk.choices[0].delta.content 