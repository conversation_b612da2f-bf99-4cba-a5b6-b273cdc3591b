# 🚀 ATLAS Trading Assistant - Comprehensive Documentation

## Table of Contents
1. [Overview](#overview)
2. [Core Features](#core-features)
3. [Trading Strategies](#trading-strategies)
4. [Options Trading](#options-trading)
5. [Installation & Setup](#installation--setup)
6. [Usage Examples](#usage-examples)
7. [API Documentation](#api-documentation)
8. [Architecture](#architecture)
9. [Risk Management](#risk-management)
10. [Troubleshooting](#troubleshooting)

## Overview

ATLAS (Automated Trading & Learning Assistant System) is a sophisticated AI-powered trading platform that combines:
- **GPT-4 Conversational AI**: Natural language trading interface
- **Real-Time Market Analysis**: 15+ trading algorithms across stocks, crypto, and options
- **Automated Trade Execution**: Via Alpaca Markets API
- **Comprehensive Risk Management**: Position sizing, stop-losses, and portfolio diversification
- **24/7 Operation**: Crypto trading and market monitoring

### What Makes ATLAS Unique
- **Natural Language Trading**: Just say "Make me $50 today" and ATLAS handles the rest
- **Intelligent Strategy Selection**: Automatically chooses the best strategy based on market conditions
- **Plain English Trade Proposals**: No complex trading jargon - everything explained simply
- **Memory System**: Remembers your preferences and past conversations
- **Multi-Asset Coverage**: Stocks, ETFs, Crypto, and Options in one platform

## Core Features

### 1. 🤖 AI-Powered Chat Interface
- **GPT-4 Integration**: Full conversational AI with trading knowledge
- **Natural Commands**: 
  - "Find me the best trades right now"
  - "What's happening with AAPL?"
  - "Show me TTM squeeze setups"
  - "Execute a covered call on my MSFT position"
- **Context Awareness**: Understands market conditions and your portfolio
- **Learning System**: Improves recommendations based on your trading style

### 2. 💼 Account Management
- **Real-Time Portfolio Tracking**:
  - Current positions with live P&L
  - Buying power and margin status
  - Day/Week/Month performance metrics
  - Position-level analytics
- **Order Management**:
  - View pending, filled, and cancelled orders
  - Modify or cancel open orders
  - Track order execution history
- **Performance Analytics**:
  - Win/loss ratios
  - Average profit per trade
  - Sharpe ratio and risk metrics

### 3. 📊 Market Data Access
- **Universal API Access**:
  - Any Alpaca endpoint: `/call_alpaca_api`
  - Any FMP endpoint: `/call_fmp_api`
- **Available Data**:
  - Real-time quotes and bars
  - Company financials and ratios
  - Economic indicators
  - Insider trading data
  - News and earnings calendars
  - Options chains and Greeks

## Trading Strategies

### Stock Trading Strategies

#### 1. **TTM Squeeze Scanner** 
- **What it finds**: Stocks experiencing volatility compression (squeeze)
- **Signals**:
  - Squeeze Fired: Volatility expansion beginning (strong signal)
  - Squeeze Active: Building pressure for potential breakout
- **Best for**: Catching explosive moves early
- **Typical holding**: 2-5 days

#### 2. **EMA Crossover (8/21)**
- **What it finds**: Momentum shifts via moving average crossovers
- **Signals**: 
  - Bullish: 8 EMA crosses above 21 EMA
  - Bearish: 8 EMA crosses below 21 EMA
- **Best for**: Trend following
- **Typical holding**: 3-10 days

#### 3. **Bollinger Band Reversion**
- **What it finds**: Extreme price extensions from the mean
- **Signals**:
  - Buy: Price touches lower band with RSI < 30
  - Sell: Price touches upper band with RSI > 70
- **Best for**: Range-bound markets
- **Typical holding**: 1-3 days

#### 4. **RSI Momentum**
- **What it finds**: Oversold/overbought conditions
- **Signals**:
  - Oversold bounce: RSI < 30 turning up
  - Overbought reversal: RSI > 70 turning down
- **Best for**: Quick reversals
- **Typical holding**: 1-5 days

#### 5. **Volume Spike Detection**
- **What it finds**: Unusual volume with price movement
- **Signals**: Volume > 2x average with price breakout
- **Best for**: Catching institutional moves
- **Typical holding**: 1-3 days

#### 6. **Donchian Channel Breakout**
- **What it finds**: Price breaking 20-day highs/lows
- **Signals**: New 20-day high (buy) or low (sell)
- **Best for**: Strong trending markets
- **Typical holding**: 5-20 days

### Crypto Trading Strategies (24/7)

#### 1. **Crypto MACD**
- **Timeframes**: 15min, 1hr, 4hr
- **Best pairs**: BTC/USD, ETH/USD, major altcoins
- **Signals**: MACD line crossovers with volume confirmation

#### 2. **Crypto RSI Extremes**
- **Oversold**: RSI < 30 on 1hr+ timeframes
- **Overbought**: RSI > 70 with divergence
- **Best for**: Major crypto pairs with high liquidity

#### 3. **On-Chain NVT Analysis**
- **Network Value to Transactions ratio**
- **Signals**: NVT < 50 (undervalued), NVT > 100 (overvalued)
- **Best for**: Long-term crypto positions

### Fundamental Strategies

#### 1. **Insider Buying Scanner**
- **Tracks**: Net insider purchases over 30 days
- **Signals**: Multiple insiders buying, CEO/CFO purchases
- **Best for**: Longer-term positions

#### 2. **Analyst Upgrade Momentum**
- **Tracks**: Recent upgrades in last 7 days
- **Signals**: Multiple upgrades, target price increases
- **Best for**: Swing trades on sentiment shift

## Options Trading

### Options Strategies Available

#### 1. **Iron Condor** 🦅
- **Market Outlook**: Neutral, expecting range-bound movement
- **Setup**: Sell OTM call spread + sell OTM put spread
- **Profit Zone**: Stock stays between short strikes
- **Max Profit**: Net credit received
- **Best When**: IV Rank > 70%, low realized volatility
- **Example Scanner Output**:
  ```
  AAPL Iron Condor opportunity:
  - IV Rank: 78%
  - Sell: 150/145 Put Spread, 160/165 Call Spread
  - Credit: $2.35
  - Profit Zone: $147.65 - $162.35
  - Probability of Profit: 68%
  ```

#### 2. **Iron Butterfly** 🦋
- **Market Outlook**: Very neutral, minimal movement expected
- **Setup**: ATM short straddle + OTM long strangle
- **Profit Zone**: Narrow range around current price
- **Max Profit**: Net credit at short strike
- **Best When**: IV Rank > 75%, expecting pin action
- **Risk/Reward**: Higher reward but narrower profit zone than condor

#### 3. **Calendar Spread** 📅
- **Market Outlook**: Neutral short-term, directional long-term
- **Setup**: Sell near-term option, buy longer-term same strike
- **Profit Zone**: Near the strike at front-month expiration
- **Best When**: IV skew between expirations > 10%
- **Management**: Roll or close at 21 DTE

#### 4. **Diagonal Spread** ↗️
- **Market Outlook**: Moderately directional with time
- **Setup**: Calendar spread with different strikes
- **Profit Zone**: Gradual move toward long strike
- **Best When**: Trending market with IV expansion
- **Example**: Sell 30-day 150C, Buy 60-day 155C

#### 5. **Vertical Spreads** 📊
- **Bull Call/Put Spreads**: Directional with defined risk
- **Bear Call/Put Spreads**: Opposite direction
- **Risk/Reward**: Typically 1:2 or better
- **Best When**: Clear directional bias, reasonable IV

#### 6. **Covered Calls** 📞
- **Requirements**: Own 100+ shares of underlying
- **Income Strategy**: Sell calls against stock position
- **Best When**: Stock near resistance, IV elevated
- **Scanner Criteria**: Min 2% monthly premium

#### 7. **Cash-Secured Puts** 💰
- **Requirements**: Cash to buy 100 shares if assigned
- **Income Strategy**: Sell puts on stocks you want to own
- **Best When**: Stock at support, willing to own
- **Scanner Criteria**: Min 2% monthly premium

#### 8. **Ratio Spreads** ⚖️
- **Advanced Strategy**: Unbalanced spreads (e.g., 1x2, 2x3)
- **Market Outlook**: Directional with volatility edge
- **Risk**: Undefined on one side
- **Best When**: High confidence in range/direction

### Intelligent Strategy Selection

ATLAS includes an **AI Strategy Analyzer** that recommends the best options strategy based on:

1. **Market Conditions**:
   - Volatility levels (VIX, IV Rank, IV Percentile)
   - Trend strength and direction
   - Support/resistance levels
   - Volume patterns

2. **Your Goals**:
   - Income generation → Covered Calls, Cash-Secured Puts
   - Neutral strategies → Iron Condor, Iron Butterfly
   - Directional plays → Vertical Spreads, Diagonals
   - Volatility trades → Calendar Spreads, Straddles

3. **Risk Tolerance**:
   - Conservative → Covered Calls, Cash-Secured Puts
   - Moderate → Vertical Spreads, Iron Condors
   - Aggressive → Ratio Spreads, Naked Options

## Installation & Setup

### Prerequisites
- Python 3.8 or higher
- Windows/Mac/Linux
- 4GB RAM minimum
- Internet connection

### Quick Install
```bash
# Clone the repository
git clone https://github.com/your-repo/atlas.git
cd atlas

# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# Mac/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### API Configuration
Create a `.env` file in the project root:

```env
# Alpaca Trading (get from https://alpaca.markets)
APCA_API_KEY_ID=your_alpaca_key_here
APCA_API_SECRET_KEY=your_alpaca_secret_here
APCA_API_BASE_URL=https://paper-api.alpaca.markets  # Use paper for testing

# OpenAI (get from https://platform.openai.com)
OPENAI_API_KEY=your_openai_key_here

# Financial Modeling Prep (get from https://financialmodelingprep.com)
FMP_KEY=your_fmp_key_here

# Optional Settings
ATLAS_RISK_PERCENT=2  # Max risk per trade (default 2%)
ATLAS_MAX_POSITIONS=10  # Max concurrent positions
ATLAS_DEFAULT_TIMEFRAME=1Day  # Default chart timeframe
```

### Running ATLAS

#### Windows - Easiest Method
```bash
# Double-click or run:
RUN_ATLAS.bat
```

#### Manual Start
```bash
# Terminal 1 - Start the server
python main.py

# Terminal 2 - Start the desktop app
python atlas.py
```

#### Alternative Interfaces
```bash
# Streamlit Dashboard (optional)
streamlit run dashboard.py

# API-only mode
uvicorn main:app --reload --port 8080
```

## Usage Examples

### Natural Language Trading

#### Example 1: Making Money
```
You: Make me $50 today with minimal risk

ATLAS: I've found a basket of 5 trades to target $50 profit:

1. AAPL - TTM Squeeze Fired Bullish
   • Buy 10 shares at $175.50
   • Stop Loss: $171.99 (-2%)
   • Target: $182.52 (+4%)
   • Risk: $35.10 | Profit: $70.20

2. MSFT - EMA Crossover Bullish
   • Buy 5 shares at $380.25
   • Stop Loss: $372.65
   • Target: $395.46
   • Risk: $38.00 | Profit: $76.05

[... 3 more trades ...]

Total Investment: $3,250
Maximum Risk: $162.50 (5% of capital)
Target Profit: $325.00
Risk/Reward: 1:2

Do you confirm this basket trade?
```

#### Example 2: Options Income
```
You: I own 500 shares of AAPL. Generate income with covered calls.

ATLAS: I found an excellent covered call opportunity on your AAPL position:

📞 AAPL Covered Call Strategy:
• Current Price: $175.50
• Sell 5 AAPL Dec 15 $180 Calls
• Premium: $3.25 per contract ($1,625 total)
• If called away: Additional $2,250 profit
• Total potential: $3,875 (4.4% in 30 days)
• Probability of keeping shares: 65%

This generates immediate income while allowing for upside to $180.

Do you want to execute this covered call strategy?
```

#### Example 3: Market Analysis
```
You: What's the best options strategy for SPY right now?

ATLAS: Based on current market conditions for SPY:

📊 Market Analysis:
• VIX: 18.5 (moderate volatility)
• IV Rank: 45% (neutral)
• Trend: Mild uptrend, testing resistance at $450
• Support: Strong at $445

🎯 Recommended Strategy: Bull Put Spread
• Sell $445 Put (30 DTE)
• Buy $440 Put (30 DTE)
• Credit: $1.85
• Max Profit: $185 per spread
• Max Loss: $315 per spread
• Probability of Profit: 72%
• Breakeven: $443.15

This takes advantage of the support level while defining risk. The mild uptrend and moderate IV make this preferable to buying calls outright.

Would you like me to calculate the position size based on your risk tolerance?
```

### Scanner Commands

#### TTM Squeeze Scanner
```
You: Find all TTM squeeze setups

ATLAS: Found 8 TTM Squeeze signals:

🔥 SQUEEZE FIRED (Strong Signals):
1. NVDA - Fired Bullish
   • Momentum: +3.2%
   • Price: $485.20
   • Volume: 150% of average
   
2. TSLA - Fired Bearish
   • Momentum: -2.8%
   • Price: $242.15
   • Volume: 120% of average

⏳ ACTIVE SQUEEZES (Building Pressure):
3. GOOGL - In squeeze 6 bars
   • Squeeze Ratio: 0.45
   • Price: $142.30
   • Coiling tight, watch for breakout
   
[... more results ...]
```

#### Options Scanner
```
You: Find high IV options trades

ATLAS: High IV Options Opportunities:

1. 🦅 ROKU Iron Condor
   • IV Rank: 89% (very high)
   • Setup: 85/80 Put, 100/105 Call
   • Credit: $2.85
   • Profit Zone: $82.15 - $102.85
   • POP: 73%

2. 🦋 COIN Iron Butterfly
   • IV Rank: 92% 
   • ATM Strike: $95
   • Credit: $4.50
   • Profit Zone: $90.50 - $99.50
   • Max Profit at $95

3. 📅 AAPL Calendar Spread
   • Front/Back IV Skew: 15%
   • Sell Dec 175C, Buy Jan 175C
   • Debit: $1.20
   • Profit from time decay
```

### Risk Management Commands

```
You: Set my maximum risk to $100 per trade

ATLAS: ✅ Risk settings updated:
• Max risk per trade: $100
• This means position sizes will be calculated to limit losses to $100
• With 2% stop losses, max position size will be $5,000
• These settings will apply to all future trades

Your current risk profile:
• Account Value: $25,000
• Risk per trade: $100 (0.4% of account)
• Max concurrent positions: 10
• Total portfolio risk: $1,000 (4% max drawdown)
```

## API Documentation

### Core Endpoints

#### Chat & AI
```python
POST /chat
{
    "user_id": "user123",
    "message": "Find me good trades",
    "context": {}  # Optional context
}

Response:
{
    "response": "I found 3 excellent opportunities...",
    "actions": ["scan_performed", "trades_found"],
    "data": {...}
}
```

#### Trading Execution
```python
POST /orchestrate_opportunity
{
    "goal": 50.0,  # Target profit
    "max_loss": 100.0,  # Maximum acceptable loss
    "markets": ["stocks", "crypto", "options"],
    "strategies": ["all"]  # Or specific ones
}

Response:
{
    "status": "success",
    "opportunities": [...],
    "best_trade": {...},
    "basket_plan": [...]
}
```

#### Strategy Scanners
```python
# Master Scanner - Runs all strategies
POST /scan_all_strategies
{
    "symbols": ["AAPL", "MSFT", ...],  # Optional
    "asset_classes": ["stocks", "options", "crypto"]
}

# Specific Scanner Example
POST /scan_ttm_squeeze
{
    "symbols": null,  # Uses default S&P 500
    "limit": 10
}

# Options Scanner Example  
POST /scan_iron_condor
{
    "symbols": ["SPY", "QQQ", "IWM"],
    "iv_rank_min": 70,
    "dte_range": [20, 45]
}
```

#### Account Management
```python
# Get Account Info
GET /account/info

# Get Positions with P&L
GET /account/positions

# Get Orders
GET /account/orders?status=all&limit=50

# Close Position
POST /close_position
{
    "symbol": "AAPL",
    "qty": 100,
    "order_type": "market"
}
```

#### Market Data
```python
# Universal Alpaca Access
POST /call_alpaca_api
{
    "endpoint": "/v2/stocks/AAPL/bars",
    "params": {
        "timeframe": "1Day",
        "limit": 100
    }
}

# Universal FMP Access
POST /call_fmp_api
{
    "endpoint": "/profile/AAPL",
    "params": {}
}
```

### WebSocket Streams (Coming Soon)
- Real-time position updates
- Live P&L tracking
- Trade alerts
- Market data streaming

## Architecture

### System Components

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│   Desktop GUI   │────▶│   FastAPI       │────▶│  LLM Brain      │
│   (atlas.py)    │     │   (main.py)     │     │ (llm_brain.py)  │
└─────────────────┘     └─────────────────┘     └─────────────────┘
                               │                          │
                               ▼                          ▼
                        ┌─────────────────┐     ┌─────────────────┐
                        │   Strategies    │     │     OpenAI      │
                        │ (strategies.py) │     │     GPT-4       │
                        └─────────────────┘     └─────────────────┘
                               │
                               ▼
                        ┌─────────────────┐     ┌─────────────────┐
                        │  Trade Engine   │────▶│     Alpaca      │
                        │  (engine.py)    │     │   Trading API   │
                        └─────────────────┘     └─────────────────┘
```

### Data Flow
1. User enters natural language command
2. LLM Brain interprets and routes to appropriate function
3. Strategies module scans markets based on request
4. Engine calculates position sizing and risk
5. Trade proposals presented in plain English
6. Upon confirmation, orders sent to Alpaca
7. Real-time monitoring of positions

### Key Files
- **atlas.py**: Tkinter desktop GUI
- **main.py**: FastAPI server with all endpoints
- **llm_brain.py**: GPT-4 integration and NLP
- **strategies.py**: All trading algorithms
- **engine.py**: Trade orchestration and risk management
- **alpaca_integration.py**: Broker API wrapper
- **plain_english_trade_template.py**: User-friendly formatting

## Risk Management

### Position Sizing Algorithm
```python
# ATLAS uses sophisticated position sizing:
position_size = min(
    capital * risk_percent / stop_loss_distance,
    capital * max_position_percent,
    available_buying_power * 0.95
)
```

### Risk Controls
1. **Per-Trade Risk**: Default 2% of account (configurable)
2. **Stop Losses**: Mandatory on every trade
3. **Position Limits**: Max 10 concurrent positions
4. **Buying Power**: Always keeps 5% reserve
5. **Time Stops**: Auto-close after X days (optional)

### Portfolio Management
- **Diversification**: Automatic sector/asset spreading
- **Correlation**: Avoids similar positions
- **Rebalancing**: Optional periodic rebalancing
- **Drawdown Limits**: Stops trading after X% loss

## Troubleshooting

### Common Issues

#### 1. "Server not responding"
```bash
# Check if server is running
curl http://localhost:8080

# Check logs
tail -f atlas.log

# Restart server
pkill -f "python main.py"
python main.py
```

#### 2. "LLM not available"
- Verify OpenAI API key in .env
- Check API quota/billing
- Test key: `python test_openai.py`

#### 3. "Orders not executing"
- Verify market hours (stocks: 9:30-4 ET)
- Check Alpaca API status
- Ensure sufficient buying power
- Verify API keys are for correct environment (paper/live)

#### 4. "Scanner returning no results"
- May indicate tight market conditions
- Try relaxing parameters
- Check if symbols are valid
- Verify market data permissions

### Debug Mode
```python
# Enable debug logging
import logging
logging.basicConfig(level=logging.DEBUG)

# Or set in environment
export ATLAS_DEBUG=true
```

### Performance Optimization
- Limit concurrent scanners to 5
- Use symbol subsets for faster scans
- Enable Redis caching (optional)
- Adjust `MAX_WORKERS` in strategies.py

## Advanced Features

### Custom Strategies
Add your own strategy to strategies.py:
```python
def scan_my_custom_strategy(symbols: List[str], **params) -> List[Dict]:
    signals = []
    for symbol in symbols:
        df = get_market_data(symbol)
        # Your logic here
        if condition_met:
            signals.append({
                "symbol": symbol,
                "algorithm": "My Custom Strategy",
                "signal": "BUY",
                "confidence": "High",
                "entry_price": current_price,
                # ... more fields
            })
    return signals
```

### Backtesting (Coming Soon)
- Historical strategy performance
- Parameter optimization
- Walk-forward analysis
- Monte Carlo simulations

### Machine Learning (Roadmap)
- Pattern recognition enhancement
- Personalized strategy selection
- Predictive position sizing
- Sentiment analysis integration

## Best Practices

### For Beginners
1. Start with paper trading
2. Use conservative position sizes
3. Focus on 1-2 strategies initially
4. Always use stop losses
5. Review trades daily

### For Advanced Users
1. Customize risk parameters
2. Create strategy combinations
3. Use options for hedging
4. Implement pairs trading
5. Add custom indicators

### General Tips
- Trade with the trend
- Don't fight the Fed
- Cut losses quickly
- Let winners run
- Keep a trading journal

## Legal & Disclaimers

**Important**: 
- This software is for educational purposes
- No guarantee of profits
- Past performance doesn't predict future results  
- You can lose money trading
- Consult a financial advisor
- The developers are not responsible for trading losses

**Regulatory**:
- Complies with PDT rules
- Respects market hours
- Follows API rate limits
- No market manipulation

## Support & Community

### Getting Help
1. Check this documentation
2. Review error logs
3. Search existing issues
4. Join our Discord (coming soon)

### Contributing
- Fork the repository
- Create feature branch
- Submit pull request
- Follow code style guide

### Roadmap
- [ ] Mobile app
- [ ] Advanced charting
- [ ] Social features
- [ ] Copy trading
- [ ] Crypto futures
- [ ] More brokers

---

**Remember**: Successful trading requires discipline, risk management, and continuous learning. ATLAS is a tool to help you, but the decisions and responsibility remain yours.

*Trade wisely. Trade with ATLAS.* 🚀 