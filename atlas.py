#!/usr/bin/env python3
"""
ATLAS Complete Desktop Application
Integrates the full ChatGPT-powered LLM brain with all trading capabilities
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import os
import sys
import threading
import asyncio
import json
import requests
from datetime import datetime
from pathlib import Path
import subprocess

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Import ATLAS components
from llm_brain import LLMBrain, Message, MessageRole
from alpaca_integration import AlpacaAccount

class ATLASCompleteDesktop:
    """Complete ATLAS desktop app with full LLM integration"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("ATLAS Trading Assistant - Complete Edition")
        self.root.geometry("1400x900")
        
        # Dark theme colors
        self.bg_color = "#1a1a1a"
        self.fg_color = "#ffffff"
        self.chat_bg = "#2d2d2d"
        self.input_bg = "#3d3d3d"
        self.accent_color = "#00d4ff"
        self.success_color = "#4caf50"
        self.error_color = "#f44336"
        
        self.root.configure(bg=self.bg_color)
        
        # Initialize message queue for system messages
        self.pending_messages = []
        
        # Create UI first
        self.setup_ui()
        
        # Then initialize components
        self.setup_components()
        
        # Show initial status
        self.update_status()
        
        # Display any pending messages
        for msg_type, msg in self.pending_messages:
            self.add_message(msg_type, msg)
        self.pending_messages.clear()
        
    def setup_components(self):
        """Initialize ATLAS components"""
        # Check if server is running
        self.server_url = "http://127.0.0.1:8080"
        self.server_running = False
        self.server_process = None
        
        # Initialize LLM Brain
        try:
            self.llm_brain = LLMBrain()
            self.llm_available = True
            self.pending_messages.append(("system", "✅ LLM Brain initialized with GPT-4"))
        except Exception as e:
            self.llm_brain = None
            self.llm_available = False
            self.pending_messages.append(("system", f"⚠️ LLM not available: {str(e)}"))
        
        # Initialize Alpaca
        try:
            self.alpaca = AlpacaAccount()
            self.alpaca_available = True
        except:
            self.alpaca = None
            self.alpaca_available = False
        
        # Start server check
        self.check_server()
        
    def check_server(self):
        """Check if FastAPI server is running"""
        try:
            response = requests.get(self.server_url, timeout=1)
            if response.status_code == 200:
                self.server_running = True
                self.pending_messages.append(("system", "✅ Server is running on port 8080"))
            else:
                self.start_server()
        except:
            self.start_server()
            
    def start_server(self):
        """Start the FastAPI server in background"""
        self.pending_messages.append(("system", "🚀 Starting ATLAS server..."))
        
        def run_server():
            try:
                self.server_process = subprocess.Popen(
                    [sys.executable, "main.py"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
                )
                
                # Wait for server to be ready
                import time
                for i in range(30):
                    try:
                        response = requests.get(self.server_url, timeout=1)
                        if response.status_code == 200:
                            self.server_running = True
                            self.root.after(0, lambda: self.add_system_message("✅ Server started successfully"))
                            return
                    except:
                        pass
                    time.sleep(1)
                    
                self.root.after(0, lambda: self.add_system_message("❌ Server failed to start"))
                
            except Exception as e:
                self.root.after(0, lambda: self.add_system_message(f"❌ Server error: {str(e)}"))
        
        threading.Thread(target=run_server, daemon=True).start()
        
    def setup_ui(self):
        """Create the main UI"""
        # Main container with padding
        main_frame = tk.Frame(self.root, bg=self.bg_color)
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Header with status
        header_frame = tk.Frame(main_frame, bg=self.bg_color)
        header_frame.pack(fill='x', pady=(0, 10))
        
        # Title
        title_label = tk.Label(
            header_frame,
            text="🤖 ATLAS Trading Assistant",
            font=('Arial', 24, 'bold'),
            bg=self.bg_color,
            fg=self.accent_color
        )
        title_label.pack(side='left')
        
        # Status indicators
        self.status_frame = tk.Frame(header_frame, bg=self.bg_color)
        self.status_frame.pack(side='right')
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill='both', expand=True)
        
        # Style the notebook
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('TNotebook', background=self.bg_color)
        style.configure('TNotebook.Tab', padding=[20, 10])
        
        # Chat Tab
        self.chat_frame = tk.Frame(self.notebook, bg=self.bg_color)
        self.notebook.add(self.chat_frame, text="💬 AI Chat")
        self.setup_chat_tab()
        
        # Positions Tab
        self.positions_frame = tk.Frame(self.notebook, bg=self.bg_color)
        self.notebook.add(self.positions_frame, text="📊 Live Positions")
        self.setup_positions_tab()
        
        # Account Tab
        self.account_frame = tk.Frame(self.notebook, bg=self.bg_color)
        self.notebook.add(self.account_frame, text="💰 Account")
        self.setup_account_tab()
        
        # Strategies Tab
        self.strategies_frame = tk.Frame(self.notebook, bg=self.bg_color)
        self.notebook.add(self.strategies_frame, text="🎯 Strategies")
        self.setup_strategies_tab()
        
    def setup_chat_tab(self):
        """Setup the AI chat interface"""
        # Chat display
        chat_container = tk.Frame(self.chat_frame, bg=self.bg_color)
        chat_container.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.chat_display = scrolledtext.ScrolledText(
            chat_container,
            wrap=tk.WORD,
            width=100,
            height=30,
            font=('Consolas', 11),
            bg=self.chat_bg,
            fg=self.fg_color,
            insertbackground=self.fg_color,
            padx=15,
            pady=15,
            relief='flat',
            borderwidth=0
        )
        self.chat_display.pack(fill='both', expand=True)
        
        # Configure tags
        self.chat_display.tag_config("user", foreground="#4fc3f7", font=('Consolas', 11, 'bold'))
        self.chat_display.tag_config("assistant", foreground="#81c784", font=('Consolas', 11, 'bold'))
        self.chat_display.tag_config("system", foreground="#9e9e9e", font=('Consolas', 10, 'italic'))
        self.chat_display.tag_config("error", foreground="#ff5252")
        self.chat_display.tag_config("trade", foreground="#ffd54f", font=('Consolas', 11, 'bold'))
        self.chat_display.tag_config("timestamp", foreground="#666666", font=('Consolas', 9))
        
        # Quick actions
        actions_frame = tk.Frame(self.chat_frame, bg=self.bg_color)
        actions_frame.pack(fill='x', padx=10, pady=5)
        
        quick_actions = [
            ("💰 Make $50", "Make me $50 today"),
            ("🔍 Scan Markets", "Scan all markets for the best opportunities"),
            ("📊 My Positions", "Show me my current positions and P&L"),
            ("🎯 Best Trade", "What's the single best trade right now?"),
            ("📚 Help", "What can you help me with?")
        ]
        
        for text, command in quick_actions:
            btn = tk.Button(
                actions_frame,
                text=text,
                command=lambda c=command: self.send_quick_message(c),
                font=('Arial', 10),
                bg=self.input_bg,
                fg=self.fg_color,
                relief='flat',
                padx=15,
                pady=8,
                cursor='hand2',
                activebackground=self.accent_color
            )
            btn.pack(side='left', padx=5)
        
        # Input area
        input_container = tk.Frame(self.chat_frame, bg=self.bg_color)
        input_container.pack(fill='x', padx=10, pady=(10, 10))
        
        # Multi-line input
        self.message_input = tk.Text(
            input_container,
            height=3,
            font=('Consolas', 11),
            bg=self.input_bg,
            fg=self.fg_color,
            insertbackground=self.fg_color,
            wrap=tk.WORD,
            padx=10,
            pady=10,
            relief='flat',
            borderwidth=0
        )
        self.message_input.pack(side='left', fill='both', expand=True)
        self.message_input.bind('<Return>', self.handle_enter)
        self.message_input.bind('<Shift-Return>', lambda e: None)
        self.message_input.focus()
        
        # Send button
        self.send_button = tk.Button(
            input_container,
            text="Send",
            command=self.send_message,
            font=('Arial', 12, 'bold'),
            bg=self.accent_color,
            fg='white',
            relief='flat',
            padx=25,
            pady=20,
            cursor='hand2',
            activebackground=self.success_color
        )
        self.send_button.pack(side='right', padx=(10, 0))
        
        # Add welcome message
        self.add_message("assistant", "Welcome to ATLAS! I'm your AI-powered trading assistant with access to real-time market data and your Alpaca account. How can I help you make money today?")
        
    def setup_positions_tab(self):
        """Setup live positions display"""
        # Controls
        controls_frame = tk.Frame(self.positions_frame, bg=self.bg_color)
        controls_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Button(
            controls_frame,
            text="🔄 Refresh",
            command=self.refresh_positions,
            bg=self.input_bg,
            fg=self.fg_color,
            relief='flat',
            padx=15,
            pady=8
        ).pack(side='left', padx=5)
        
        self.auto_refresh_var = tk.BooleanVar(value=True)
        tk.Checkbutton(
            controls_frame,
            text="Auto-refresh (5s)",
            variable=self.auto_refresh_var,
            bg=self.bg_color,
            fg=self.fg_color,
            selectcolor=self.bg_color
        ).pack(side='left', padx=20)
        
        # Positions table
        columns = ("Symbol", "Qty", "Entry", "Current", "P&L $", "P&L %", "Value", "Action")
        self.positions_tree = ttk.Treeview(
            self.positions_frame,
            columns=columns,
            show='headings',
            height=20
        )
        
        for col in columns:
            self.positions_tree.heading(col, text=col)
            self.positions_tree.column(col, width=100)
        
        self.positions_tree.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Summary
        self.positions_summary = tk.Label(
            self.positions_frame,
            text="Total P&L: $0.00",
            font=('Arial', 14, 'bold'),
            bg=self.bg_color,
            fg=self.fg_color
        )
        self.positions_summary.pack(pady=10)
        
        # Start auto-refresh
        self.auto_refresh_positions()
        
    def setup_account_tab(self):
        """Setup account information display"""
        # Account metrics grid
        metrics_frame = tk.Frame(self.account_frame, bg=self.bg_color)
        metrics_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        self.account_labels = {}
        metrics = [
            ("Portfolio Value", "$0.00"),
            ("Buying Power", "$0.00"),
            ("Cash", "$0.00"),
            ("Day Trades", "0/3"),
            ("Total P&L", "$0.00"),
            ("Today's P&L", "$0.00"),
            ("Positions", "0"),
            ("Orders", "0")
        ]
        
        for i, (label, value) in enumerate(metrics):
            row = i // 2
            col = i % 2
            
            # Label
            tk.Label(
                metrics_frame,
                text=f"{label}:",
                font=('Arial', 12, 'bold'),
                bg=self.bg_color,
                fg=self.fg_color
            ).grid(row=row, column=col*2, padx=20, pady=15, sticky='e')
            
            # Value
            value_label = tk.Label(
                metrics_frame,
                text=value,
                font=('Arial', 14),
                bg=self.bg_color,
                fg=self.accent_color
            )
            value_label.grid(row=row, column=col*2+1, padx=(0, 40), pady=15, sticky='w')
            self.account_labels[label] = value_label
        
        # Refresh button
        tk.Button(
            metrics_frame,
            text="🔄 Refresh Account",
            command=self.refresh_account,
            bg=self.accent_color,
            fg='white',
            relief='flat',
            padx=20,
            pady=10,
            font=('Arial', 12, 'bold')
        ).grid(row=4, column=0, columnspan=4, pady=20)
        
    def setup_strategies_tab(self):
        """Setup strategies scanner"""
        # Scanner controls
        controls_frame = tk.Frame(self.strategies_frame, bg=self.bg_color)
        controls_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Label(
            controls_frame,
            text="Select strategies to scan:",
            font=('Arial', 12),
            bg=self.bg_color,
            fg=self.fg_color
        ).pack(side='left', padx=10)
        
        tk.Button(
            controls_frame,
            text="🚀 Run Full Scan",
            command=self.run_strategy_scan,
            bg=self.success_color,
            fg='white',
            relief='flat',
            padx=20,
            pady=10,
            font=('Arial', 12, 'bold')
        ).pack(side='right', padx=10)
        
        # Results display
        self.strategy_results = scrolledtext.ScrolledText(
            self.strategies_frame,
            wrap=tk.WORD,
            width=100,
            height=25,
            font=('Consolas', 10),
            bg=self.chat_bg,
            fg=self.fg_color
        )
        self.strategy_results.pack(fill='both', expand=True, padx=10, pady=10)
        
    def handle_enter(self, event):
        """Handle enter key press"""
        if not event.state & 0x1:  # Shift not pressed
            self.send_message()
            return 'break'
            
    def send_message(self):
        """Send message to LLM"""
        message = self.message_input.get('1.0', 'end-1c').strip()
        if not message:
            return
            
        self.message_input.delete('1.0', tk.END)
        self.add_message("user", message)
        
        # Process in background
        threading.Thread(target=self.process_message, args=(message,), daemon=True).start()
        
    def process_message(self, message):
        """Process message with LLM brain"""
        try:
            if self.llm_available and self.server_running:
                # Use full LLM processing
                self.root.after(0, lambda: self.add_system_message("🤔 Thinking..."))
                
                # Create async event loop for the thread
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                # Process with LLM
                try:
                    response, actions = loop.run_until_complete(
                        self.llm_brain.process_message("desktop_user", message)
                    )
                    
                    # Add response
                    self.root.after(0, lambda: self.add_message("assistant", response))
                    
                    # Handle any actions
                    if actions:
                        self.handle_actions(actions)
                except Exception as llm_error:
                    # If LLM fails, try server chat
                    self.root.after(0, lambda: self.add_system_message(f"⚠️ LLM error, using server chat..."))
                    self.call_server_chat(message)
                    
            else:
                # Fallback to server API
                self.call_server_chat(message)
                
        except Exception as e:
            self.root.after(0, lambda: self.add_message("error", f"Error: {str(e)}"))
            
    def call_server_chat(self, message):
        """Call server chat endpoint"""
        try:
            response = requests.post(
                f"{self.server_url}/chat",
                json={
                    "user_id": "desktop_user",
                    "message": message
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                self.root.after(0, lambda: self.add_message("assistant", data.get("response", "No response")))
            else:
                self.root.after(0, lambda: self.add_message("error", f"Server error: {response.status_code}"))
                
        except Exception as e:
            error_msg = f"Connection error: {str(e)}"
            self.root.after(0, lambda msg=error_msg: self.add_message("error", msg))
            
    def handle_actions(self, actions):
        """Handle LLM-requested actions"""
        if "function_called" in actions:
            func = actions["function_called"]
            if func == "call_alpaca_api" and "positions" in actions.get("function_args", {}).get("path", ""):
                self.root.after(0, lambda: self.notebook.select(1))  # Switch to positions tab
                self.root.after(0, self.refresh_positions)
            elif func == "orchestrate_opportunity":
                # Trade proposal was made
                pass
                
    def send_quick_message(self, message):
        """Send a quick message"""
        self.message_input.insert('1.0', message)
        self.send_message()
        
    def add_message(self, sender, message):
        """Add message to chat display"""
        timestamp = datetime.now().strftime("%I:%M %p")
        
        self.chat_display.config(state='normal')
        
        # Add timestamp
        self.chat_display.insert('end', f"[{timestamp}] ", 'timestamp')
        
        # Add sender and message
        if sender == "user":
            self.chat_display.insert('end', "You: ", 'user')
        elif sender == "assistant":
            self.chat_display.insert('end', "ATLAS: ", 'assistant')
        elif sender == "error":
            self.chat_display.insert('end', "Error: ", 'error')
        elif sender == "system":
            self.chat_display.insert('end', "System: ", 'system')
            
        self.chat_display.insert('end', f"{message}\n\n")
        self.chat_display.see('end')
        self.chat_display.config(state='disabled')
        
    def add_system_message(self, message):
        """Add system message"""
        self.add_message("system", message)
        
    def refresh_positions(self):
        """Refresh positions display"""
        if not self.server_running:
            return
            
        try:
            response = requests.get(f"{self.server_url}/account/positions", timeout=10)
            if response.status_code == 200:
                positions = response.json()
                
                # Clear existing
                for item in self.positions_tree.get_children():
                    self.positions_tree.delete(item)
                
                # Add positions
                total_pl = 0
                if isinstance(positions, list):
                    for pos in positions:
                        if isinstance(pos, dict):
                            pl = float(pos.get('unrealized_pl', 0))
                            total_pl += pl
                            
                            values = (
                                pos.get('symbol', ''),
                                pos.get('qty', 0),
                                f"${float(pos.get('avg_entry_price', 0)):.2f}",
                                f"${float(pos.get('current_price', 0)):.2f}",
                                f"${pl:.2f}",
                                f"{float(pos.get('unrealized_plpc', 0))*100:.1f}%",
                                f"${float(pos.get('market_value', 0)):.2f}",
                                "Double-click to exit"
                            )
                            
                            # Color based on P&L
                            tag = "profit" if pl >= 0 else "loss"
                            self.positions_tree.insert("", tk.END, values=values, tags=(tag,))
                
                # Update summary
                color = self.success_color if total_pl >= 0 else self.error_color
                self.positions_summary.config(
                    text=f"Total P&L: ${total_pl:,.2f}",
                    fg=color
                )
                
        except Exception as e:
            self.add_system_message(f"Failed to refresh positions: {str(e)}")
            
    def auto_refresh_positions(self):
        """Auto-refresh positions"""
        if self.auto_refresh_var.get() and self.notebook.index("current") == 1:
            self.refresh_positions()
        self.root.after(5000, self.auto_refresh_positions)
        
    def refresh_account(self):
        """Refresh account information"""
        if not self.server_running:
            return
            
        try:
            response = requests.get(f"{self.server_url}/account/info", timeout=10)
            if response.status_code == 200:
                account = response.json()
                
                if isinstance(account, dict) and "error" not in account:
                    # Update labels
                    self.account_labels["Portfolio Value"].config(
                        text=f"${float(account.get('portfolio_value', 0)):,.2f}"
                    )
                    self.account_labels["Buying Power"].config(
                        text=f"${float(account.get('buying_power', 0)):,.2f}"
                    )
                    self.account_labels["Cash"].config(
                        text=f"${float(account.get('cash', 0)):,.2f}"
                    )
                    self.account_labels["Day Trades"].config(
                        text=f"{account.get('daytrade_count', 0)}/3"
                    )
                    
                    # Get positions count
                    pos_response = requests.get(f"{self.server_url}/account/positions", timeout=5)
                    if pos_response.status_code == 200:
                        positions = pos_response.json()
                        if isinstance(positions, list):
                            self.account_labels["Positions"].config(text=str(len(positions)))
                            
        except Exception as e:
            self.add_system_message(f"Failed to refresh account: {str(e)}")
            
    def run_strategy_scan(self):
        """Run full strategy scan"""
        self.strategy_results.delete('1.0', tk.END)
        self.strategy_results.insert('end', "🔍 Running full market scan...\n\n")
        
        def scan():
            try:
                response = requests.post(
                    f"{self.server_url}/scan_all_strategies",
                    json={},
                    timeout=60
                )
                
                if response.status_code == 200:
                    results = response.json()
                    
                    # Format results
                    output = "📊 STRATEGY SCAN RESULTS\n" + "="*50 + "\n\n"
                    
                    if isinstance(results, dict) and "data" in results:
                        data = results["data"]
                        for strategy, signals in data.items():
                            if signals:
                                output += f"\n🎯 {strategy.upper()}\n"
                                output += "-" * 30 + "\n"
                                for signal in signals[:5]:  # Top 5 per strategy
                                    output += f"  {signal.get('symbol', 'N/A')}: {signal.get('signal', 'N/A')}\n"
                                    output += f"    Entry: ${signal.get('entry_price', 0):.2f}\n"
                                    output += f"    Confidence: {signal.get('confidence', 'N/A')}\n\n"
                    
                    self.root.after(0, lambda: self.strategy_results.insert('end', output))
                    
            except Exception as e:
                self.root.after(0, lambda: self.strategy_results.insert('end', f"\n❌ Error: {str(e)}"))
                
        threading.Thread(target=scan, daemon=True).start()
        
    def update_status(self):
        """Update status indicators"""
        # Clear existing
        for widget in self.status_frame.winfo_children():
            widget.destroy()
            
        # LLM status
        llm_status = "🟢 GPT-4" if self.llm_available else "🔴 LLM Offline"
        tk.Label(
            self.status_frame,
            text=llm_status,
            bg=self.bg_color,
            fg=self.fg_color,
            font=('Arial', 10)
        ).pack(side='left', padx=10)
        
        # Server status
        server_status = "🟢 Server" if self.server_running else "🔴 Server Offline"
        tk.Label(
            self.status_frame,
            text=server_status,
            bg=self.bg_color,
            fg=self.fg_color,
            font=('Arial', 10)
        ).pack(side='left', padx=10)
        
        # Alpaca status
        alpaca_status = "🟢 Alpaca" if self.alpaca_available else "🔴 Alpaca Offline"
        tk.Label(
            self.status_frame,
            text=alpaca_status,
            bg=self.bg_color,
            fg=self.fg_color,
            font=('Arial', 10)
        ).pack(side='left', padx=10)
        
    def on_closing(self):
        """Handle window closing"""
        if self.server_process:
            self.server_process.terminate()
        self.root.destroy()
        
    def run(self):
        """Run the application"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

def main():
    """Main entry point"""
    # Set environment variables if not already set
    if not os.getenv("OPENAI_API_KEY"):
        # Use the hardcoded key from llm_brain.py
        os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"
    
    # Set dummy values for other keys if not set
    if not os.getenv("APCA_API_KEY_ID"):
        os.environ["APCA_API_KEY_ID"] = "dummy_key"
    if not os.getenv("APCA_API_SECRET_KEY"):
        os.environ["APCA_API_SECRET_KEY"] = "dummy_secret"
    if not os.getenv("FMP_KEY"):
        os.environ["FMP_KEY"] = "dummy_fmp"
    
    # Run the app
    app = ATLASCompleteDesktop()
    app.run()

if __name__ == "__main__":
    main() 