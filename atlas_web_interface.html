<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ATLAS Paper Trading Assistant</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0a0a0a;
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
        }
        
        .container {
            display: grid;
            grid-template-columns: 300px 1fr 350px;
            height: 100vh;
            gap: 1px;
            background: #1a1a1a;
        }
        
        /* Left Sidebar - Account Info */
        .sidebar-left {
            background: #0f0f0f;
            padding: 20px;
            overflow-y: auto;
        }
        
        .account-card {
            background: #1a1a1a;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #2a2a2a;
        }
        
        .account-card h3 {
            color: #888;
            font-size: 14px;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .account-value {
            font-size: 32px;
            font-weight: bold;
            color: #4CAF50;
            margin-bottom: 5px;
        }
        
        .account-change {
            font-size: 14px;
            color: #4CAF50;
        }
        
        .account-change.negative {
            color: #f44336;
        }
        
        /* Center - Chat Interface */
        .main-content {
            background: #0f0f0f;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: #1a1a1a;
            padding: 20px;
            border-bottom: 1px solid #2a2a2a;
            text-align: center;
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 5px;
            background: linear-gradient(45deg, #4CAF50, #2196F3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .header p {
            color: #888;
            font-size: 14px;
        }
        
        .paper-trading-badge {
            display: inline-block;
            background: #ff9800;
            color: #000;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            margin-top: 10px;
        }
        
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 20px;
            overflow: hidden;
        }
        
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #1a1a1a;
            border-radius: 12px;
            margin-bottom: 20px;
        }
        
        .message {
            margin-bottom: 20px;
            animation: fadeIn 0.3s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .message.user {
            text-align: right;
        }
        
        .message-content {
            display: inline-block;
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }
        
        .message.user .message-content {
            background: #2196F3;
            color: white;
        }
        
        .message.assistant .message-content {
            background: #2a2a2a;
            color: #fff;
            border: 1px solid #3a3a3a;
        }
        
        .message.system .message-content {
            background: #ff9800;
            color: #000;
            font-weight: 500;
        }
        
        .chat-input-container {
            display: flex;
            gap: 10px;
        }
        
        .chat-input {
            flex: 1;
            background: #1a1a1a;
            border: 1px solid #3a3a3a;
            color: white;
            padding: 15px 20px;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }
        
        .chat-input:focus {
            border-color: #2196F3;
        }
        
        .send-button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .send-button:hover {
            background: #1976D2;
        }
        
        .send-button:disabled {
            background: #555;
            cursor: not-allowed;
        }
        
        /* Right Sidebar - Positions & Strategies */
        .sidebar-right {
            background: #0f0f0f;
            padding: 20px;
            overflow-y: auto;
        }
        
        .positions-section, .strategies-section {
            margin-bottom: 30px;
        }
        
        .section-title {
            color: #888;
            font-size: 14px;
            margin-bottom: 15px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .position-card {
            background: #1a1a1a;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border: 1px solid #2a2a2a;
            transition: border-color 0.3s;
        }
        
        .position-card:hover {
            border-color: #3a3a3a;
        }
        
        .position-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .position-symbol {
            font-weight: bold;
            font-size: 16px;
        }
        
        .position-pnl {
            font-weight: bold;
        }
        
        .position-pnl.positive {
            color: #4CAF50;
        }
        
        .position-pnl.negative {
            color: #f44336;
        }
        
        .position-details {
            font-size: 12px;
            color: #888;
        }
        
        .strategy-button {
            width: 100%;
            background: #1a1a1a;
            border: 1px solid #2a2a2a;
            color: white;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 8px;
            cursor: pointer;
            text-align: left;
            transition: all 0.3s;
        }
        
        .strategy-button:hover {
            background: #2a2a2a;
            border-color: #3a3a3a;
        }
        
        .strategy-button.active {
            background: #2196F3;
            border-color: #2196F3;
        }
        
        /* Status indicators */
        .status-bar {
            background: #1a1a1a;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 1px solid #2a2a2a;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: #888;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4CAF50;
        }
        
        .status-dot.offline {
            background: #f44336;
        }
        
        /* Loading spinner */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #2196F3;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Quick actions */
        .quick-actions {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .quick-action {
            background: #2a2a2a;
            border: 1px solid #3a3a3a;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .quick-action:hover {
            background: #3a3a3a;
            border-color: #4a4a4a;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Left Sidebar - Account Info -->
        <div class="sidebar-left">
            <div class="account-card">
                <h3>Portfolio Value</h3>
                <div class="account-value" id="portfolioValue">$100,000</div>
                <div class="account-change" id="portfolioChange">+$0 (0%)</div>
            </div>
            
            <div class="account-card">
                <h3>Buying Power</h3>
                <div class="account-value" id="buyingPower">$100,000</div>
            </div>
            
            <div class="account-card">
                <h3>Today's P&L</h3>
                <div class="account-value" id="dailyPnl">$0</div>
                <div class="account-change" id="dailyPnlPercent">0%</div>
            </div>
            
            <div class="account-card">
                <h3>Market Status</h3>
                <div style="margin-top: 10px;">
                    <div class="status-item">
                        <span class="status-dot" id="stocksStatus"></span>
                        <span>Stocks: <span id="stocksStatusText">Checking...</span></span>
                    </div>
                    <div class="status-item" style="margin-top: 8px;">
                        <span class="status-dot" id="cryptoStatus"></span>
                        <span>Crypto: <span id="cryptoStatusText">24/7</span></span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Center - Chat Interface -->
        <div class="main-content">
            <div class="header">
                <h1>ATLAS Trading Assistant</h1>
                <p>AI-Powered Trading with Natural Language</p>
                <div class="paper-trading-badge">PAPER TRADING MODE</div>
            </div>
            
            <div class="chat-container">
                <div class="quick-actions">
                    <button class="quick-action" onclick="sendQuickMessage('Show me my account status')">Account Status</button>
                    <button class="quick-action" onclick="sendQuickMessage('Find the best trades right now')">Find Trades</button>
                    <button class="quick-action" onclick="sendQuickMessage('What are the top movers today?')">Top Movers</button>
                    <button class="quick-action" onclick="sendQuickMessage('Scan for options opportunities')">Options Scan</button>
                    <button class="quick-action" onclick="sendQuickMessage('Show me crypto opportunities')">Crypto Scan</button>
                </div>
                
                <div class="chat-messages" id="chatMessages">
                    <div class="message assistant">
                        <div class="message-content">
                            Welcome to ATLAS Paper Trading! I'm your AI trading assistant. I can help you:
                            <br><br>
                            • Find trading opportunities across stocks, crypto, and options<br>
                            • Execute paper trades with proper risk management<br>
                            • Monitor your positions and P&L<br>
                            • Analyze market conditions and trends<br>
                            <br>
                            Try saying "Make me $50 today" or "Find the best trades right now"!
                        </div>
                    </div>
                </div>
                
                <div class="chat-input-container">
                    <input 
                        type="text" 
                        class="chat-input" 
                        id="chatInput" 
                        placeholder="Ask me anything about trading..."
                        onkeypress="if(event.key==='Enter' && !event.shiftKey) sendMessage()"
                    >
                    <button class="send-button" id="sendButton" onclick="sendMessage()">Send</button>
                </div>
            </div>
            
            <div class="status-bar">
                <div class="status-item">
                    <span class="status-dot" id="serverStatus"></span>
                    <span>Server: <span id="serverStatusText">Connected</span></span>
                </div>
                <div class="status-item">
                    <span id="lastUpdate">Last update: Just now</span>
                </div>
            </div>
        </div>
        
        <!-- Right Sidebar - Positions & Strategies -->
        <div class="sidebar-right">
            <div class="positions-section">
                <h3 class="section-title">Open Positions</h3>
                <div id="positionsList">
                    <div style="color: #666; text-align: center; padding: 20px;">
                        No open positions
                    </div>
                </div>
            </div>
            
            <div class="strategies-section">
                <h3 class="section-title">Active Strategies</h3>
                <button class="strategy-button" onclick="scanStrategy('ttm_squeeze')">
                    TTM Squeeze Scanner
                </button>
                <button class="strategy-button" onclick="scanStrategy('iron_condor')">
                    Iron Condor (High IV)
                </button>
                <button class="strategy-button" onclick="scanStrategy('crypto_rsi')">
                    Crypto RSI Oversold
                </button>
                <button class="strategy-button" onclick="scanStrategy('volume_spike')">
                    Volume Spike Momentum
                </button>
                <button class="strategy-button" onclick="scanStrategy('all')">
                    Scan All Strategies
                </button>
            </div>
        </div>
    </div>
    
    <script>
        // Configuration
        const API_BASE = 'http://localhost:8080';
        let isLoading = false;
        
        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            updateAccountInfo();
            updateMarketStatus();
            updatePositions();
            
            // Update every 5 seconds
            setInterval(updateAccountInfo, 5000);
            setInterval(updateMarketStatus, 5000);
            setInterval(updatePositions, 5000);
        });
        
        // Send message to ATLAS
        async function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();
            
            if (!message || isLoading) return;
            
            // Add user message
            addMessage(message, 'user');
            input.value = '';
            
            // Disable input while processing
            isLoading = true;
            document.getElementById('sendButton').disabled = true;
            input.disabled = true;
            
            try {
                const response = await fetch(`${API_BASE}/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: 'web_user',
                        message: message
                    })
                });
                
                const data = await response.json();
                
                if (data.response) {
                    addMessage(data.response, 'assistant');
                } else {
                    addMessage('Sorry, I encountered an error. Please try again.', 'assistant');
                }
                
                // Update UI after trade actions
                if (message.toLowerCase().includes('trade') || 
                    message.toLowerCase().includes('buy') || 
                    message.toLowerCase().includes('sell')) {
                    setTimeout(() => {
                        updateAccountInfo();
                        updatePositions();
                    }, 1000);
                }
                
            } catch (error) {
                console.error('Error:', error);
                addMessage('Connection error. Please make sure the ATLAS server is running.', 'system');
            } finally {
                isLoading = false;
                document.getElementById('sendButton').disabled = false;
                input.disabled = false;
                input.focus();
            }
        }
        
        // Quick message helper
        function sendQuickMessage(message) {
            document.getElementById('chatInput').value = message;
            sendMessage();
        }
        
        // Add message to chat
        function addMessage(text, type) {
            const messagesDiv = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.innerHTML = text.replace(/\n/g, '<br>');
            
            messageDiv.appendChild(contentDiv);
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }
        
        // Update account information
        async function updateAccountInfo() {
            try {
                const response = await fetch(`${API_BASE}/account/info`);
                const data = await response.json();
                
                // Handle both wrapped and unwrapped responses
                const account = data.account || data;
                
                if (!account.error && account.portfolio_value !== undefined) {
                    // Update portfolio value
                    document.getElementById('portfolioValue').textContent = 
                        `$${parseFloat(account.portfolio_value).toLocaleString()}`;
                    
                    // Update buying power
                    document.getElementById('buyingPower').textContent = 
                        `$${parseFloat(account.buying_power).toLocaleString()}`;
                    
                    // Calculate daily P&L
                    const equity = parseFloat(account.equity || account.portfolio_value);
                    const lastEquity = parseFloat(account.last_equity || equity);
                    const dailyPnl = account.total_profit_loss || (equity - lastEquity);
                    const dailyPnlPercent = account.profit_loss_percent || ((dailyPnl / lastEquity) * 100);
                    
                    document.getElementById('dailyPnl').textContent = 
                        `$${dailyPnl.toFixed(2)}`;
                    document.getElementById('dailyPnlPercent').textContent = 
                        `${dailyPnlPercent >= 0 ? '+' : ''}${dailyPnlPercent.toFixed(2)}%`;
                    
                    // Update colors
                    const pnlElements = [
                        document.getElementById('dailyPnl'),
                        document.getElementById('dailyPnlPercent')
                    ];
                    
                    pnlElements.forEach(el => {
                        if (dailyPnl >= 0) {
                            el.style.color = '#4CAF50';
                        } else {
                            el.style.color = '#f44336';
                        }
                    });
                } else if (account.error) {
                    console.error('Account API error:', account.error);
                    // Show error in UI
                    document.getElementById('portfolioValue').textContent = 'Error';
                    document.getElementById('buyingPower').textContent = 'Error';
                    document.getElementById('dailyPnl').textContent = 'Error';
                }
                
                // Update last update time
                document.getElementById('lastUpdate').textContent = 
                    `Last update: ${new Date().toLocaleTimeString()}`;
                    
            } catch (error) {
                console.error('Error updating account:', error);
            }
        }
        
        // Update market status
        async function updateMarketStatus() {
            try {
                const response = await fetch(`${API_BASE}/market/status`);
                const data = await response.json();
                
                // Update stocks status - handle Alpaca's response format
                const stocksOpen = data.is_open || data.stocks_open || false;
                document.getElementById('stocksStatus').className = 
                    `status-dot ${stocksOpen ? '' : 'offline'}`;
                document.getElementById('stocksStatusText').textContent = 
                    stocksOpen ? 'Open' : 'Closed';
                
                // Crypto is always open
                document.getElementById('cryptoStatus').className = 'status-dot';
                document.getElementById('cryptoStatusText').textContent = '24/7';
                
            } catch (error) {
                console.error('Error updating market status:', error);
                document.getElementById('stocksStatusText').textContent = 'Unknown';
            }
        }
        
        // Update positions
        async function updatePositions() {
            try {
                const response = await fetch(`${API_BASE}/account/positions`);
                const data = await response.json();
                
                const positionsList = document.getElementById('positionsList');
                
                // Handle both wrapped and unwrapped responses
                const positions = data.positions || (Array.isArray(data) ? data : []);
                
                if (positions.length > 0) {
                    positionsList.innerHTML = positions.map(position => {
                        const pnl = parseFloat(position.unrealized_pl);
                        const pnlPercent = parseFloat(position.unrealized_plpc) * 100;
                        const pnlClass = pnl >= 0 ? 'positive' : 'negative';
                        
                        return `
                            <div class="position-card">
                                <div class="position-header">
                                    <span class="position-symbol">${position.symbol}</span>
                                    <span class="position-pnl ${pnlClass}">
                                        ${pnl >= 0 ? '+' : ''}$${pnl.toFixed(2)}
                                    </span>
                                </div>
                                <div class="position-details">
                                    ${position.qty} shares @ $${parseFloat(position.avg_entry_price).toFixed(2)}
                                    <br>
                                    Current: $${parseFloat(position.current_price).toFixed(2)} 
                                    (${pnlPercent >= 0 ? '+' : ''}${pnlPercent.toFixed(2)}%)
                                </div>
                            </div>
                        `;
                    }).join('');
                } else {
                    positionsList.innerHTML = `
                        <div style="color: #666; text-align: center; padding: 20px;">
                            No open positions
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Error updating positions:', error);
            }
        }
        
        // Scan specific strategy
        async function scanStrategy(strategy) {
            let message = '';
            
            switch(strategy) {
                case 'ttm_squeeze':
                    message = 'Scan for TTM Squeeze setups';
                    break;
                case 'iron_condor':
                    message = 'Find iron condor opportunities';
                    break;
                case 'crypto_rsi':
                    message = 'Show me oversold crypto';
                    break;
                case 'volume_spike':
                    message = 'Find stocks with volume spikes';
                    break;
                case 'all':
                    message = 'Scan all strategies and find the best opportunities';
                    break;
            }
            
            if (message) {
                sendQuickMessage(message);
            }
        }
        
        // Server status check
        async function checkServerStatus() {
            try {
                const response = await fetch(`${API_BASE}/`);
                if (response.ok) {
                    document.getElementById('serverStatus').className = 'status-dot';
                    document.getElementById('serverStatusText').textContent = 'Connected';
                } else {
                    throw new Error('Server error');
                }
            } catch (error) {
                document.getElementById('serverStatus').className = 'status-dot offline';
                document.getElementById('serverStatusText').textContent = 'Disconnected';
            }
        }
        
        // Check server status every 10 seconds
        setInterval(checkServerStatus, 10000);
        checkServerStatus();
    </script>
</body>
</html> 