#!/usr/bin/env python3
"""
ATLAS Simple Launcher
Single entry point for the trading system
"""

import sys
import subprocess
import time
import webbrowser
import os
from pathlib import Path
import signal
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ATLASLauncher:
    def __init__(self):
        self.server_process = None
        self.project_root = Path(__file__).parent
        
    def check_environment(self):
        """Check if environment is properly configured"""
        logger.info("Checking environment configuration...")
        
        # Check for .env file
        env_file = self.project_root / '.env'
        if not env_file.exists():
            logger.warning("No .env file found. Creating template...")
            self.create_env_template()
            return False
            
        # Check for required environment variables
        required_vars = ['APCA_API_KEY_ID', 'APCA_API_SECRET_KEY', 'OPENAI_API_KEY']
        missing_vars = []
        
        # Load .env file
        try:
            from dotenv import load_dotenv
            load_dotenv()
        except ImportError:
            logger.warning("python-dotenv not installed, skipping .env loading")
        
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
                
        if missing_vars:
            logger.error(f"Missing required environment variables: {', '.join(missing_vars)}")
            logger.info("Please edit the .env file and add your API keys")
            return False
            
        logger.info("✅ Environment configured correctly")
        return True
        
    def create_env_template(self):
        """Create a template .env file"""
        template = """# ATLAS Configuration File
# Please add your API keys below

# Alpaca Trading API (get from https://alpaca.markets)
APCA_API_KEY_ID=your_alpaca_key_here
APCA_API_SECRET_KEY=your_alpaca_secret_here
APCA_API_BASE_URL=https://paper-api.alpaca.markets

# OpenAI API (get from https://platform.openai.com)
OPENAI_API_KEY=your_openai_key_here

# Financial Modeling Prep (get from https://financialmodelingprep.com)
FMP_KEY=your_fmp_key_here

# Optional Settings
ATLAS_RISK_PERCENT=2
ATLAS_MAX_POSITIONS=10
ATLAS_DEFAULT_TIMEFRAME=1Day
"""
        
        env_file = self.project_root / '.env'
        env_file.write_text(template, encoding='utf-8')
        logger.info(f"Created .env template at {env_file}")
        
    def check_dependencies(self):
        """Check if all required packages are installed"""
        logger.info("Checking dependencies...")
        
        try:
            import fastapi
            import openai
            import pandas
            import numpy
            import requests
            logger.info("✅ All core dependencies installed")
            return True
        except ImportError as e:
            logger.error(f"Missing dependency: {e}")
            logger.info("Please run: pip install -r requirements.txt")
            return False
            
    def start_server(self):
        """Start the FastAPI server"""
        logger.info("Starting ATLAS server...")
        
        # Check if server is already running
        import requests
        try:
            response = requests.get("http://localhost:8080", timeout=1)
            if response.status_code == 200:
                logger.info("✅ Server is already running")
                return True
        except:
            pass
            
        # Start new server process
        try:
            self.server_process = subprocess.Popen(
                [sys.executable, "main.py"],
                cwd=self.project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # Wait for server to start
            logger.info("Waiting for server to start...")
            for i in range(10):
                time.sleep(1)
                try:
                    response = requests.get("http://localhost:8080", timeout=1)
                    if response.status_code == 200:
                        logger.info("✅ Server started successfully")
                        return True
                except:
                    continue
                    
            logger.error("Server failed to start")
            return False
            
        except Exception as e:
            logger.error(f"Failed to start server: {e}")
            return False
            
    def open_interface(self):
        """Open the web interface"""
        logger.info("Opening web interface...")
        
        # Create a simple HTML interface if none exists
        interface_file = self.project_root / 'atlas_interface.html'
        if not interface_file.exists():
            self.create_simple_interface()
            
        # Open in browser
        webbrowser.open("http://localhost:8080")
        logger.info("✅ Web interface opened")
        
    def create_simple_interface(self):
        """Create a simple web interface"""
        html_content = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ATLAS Trading Assistant</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .header {
            background: #1a1a1a;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .chat-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            height: 500px;
            display: flex;
            flex-direction: column;
        }
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        .chat-input {
            display: flex;
            gap: 10px;
        }
        .chat-input input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        .chat-input button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .chat-input button:hover {
            background: #0056b3;
        }
        .message {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .user-message {
            background: #e3f2fd;
            text-align: right;
        }
        .assistant-message {
            background: #f5f5f5;
        }
        .status {
            display: flex;
            gap: 20px;
            margin-top: 20px;
        }
        .status-item {
            background: white;
            padding: 15px;
            border-radius: 5px;
            flex: 1;
            text-align: center;
        }
        .status-item h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .status-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 ATLAS Trading Assistant</h1>
        <p>AI-Powered Trading with Natural Language Interface</p>
    </div>
    
    <div class="chat-container">
        <div class="chat-messages" id="chatMessages">
            <div class="message assistant-message">
                Welcome to ATLAS! I'm your AI trading assistant. Try saying:
                <ul>
                    <li>"Show me my account status"</li>
                    <li>"Find the best trades right now"</li>
                    <li>"What's happening with AAPL?"</li>
                    <li>"Make me $50 today"</li>
                </ul>
            </div>
        </div>
        <div class="chat-input">
            <input type="text" id="messageInput" placeholder="Type your message here..." 
                   onkeypress="if(event.key==='Enter') sendMessage()">
            <button onclick="sendMessage()">Send</button>
        </div>
    </div>
    
    <div class="status">
        <div class="status-item">
            <h3>Server Status</h3>
            <div class="status-value" id="serverStatus">Checking...</div>
        </div>
        <div class="status-item">
            <h3>Market Status</h3>
            <div class="status-value" id="marketStatus">Checking...</div>
        </div>
        <div class="status-item">
            <h3>Active Positions</h3>
            <div class="status-value" id="positions">0</div>
        </div>
    </div>
    
    <script>
        // Check server status
        async function checkStatus() {
            try {
                const response = await fetch('http://localhost:8080/');
                if (response.ok) {
                    document.getElementById('serverStatus').textContent = '✅ Online';
                    document.getElementById('serverStatus').style.color = '#28a745';
                } else {
                    document.getElementById('serverStatus').textContent = '❌ Offline';
                    document.getElementById('serverStatus').style.color = '#dc3545';
                }
            } catch (error) {
                document.getElementById('serverStatus').textContent = '❌ Offline';
                document.getElementById('serverStatus').style.color = '#dc3545';
            }
            
            // Check market status
            try {
                const response = await fetch('http://localhost:8080/market/status');
                const data = await response.json();
                if (data.stocks_open) {
                    document.getElementById('marketStatus').textContent = '🟢 Open';
                    document.getElementById('marketStatus').style.color = '#28a745';
                } else {
                    document.getElementById('marketStatus').textContent = '🔴 Closed';
                    document.getElementById('marketStatus').style.color = '#dc3545';
                }
            } catch (error) {
                document.getElementById('marketStatus').textContent = 'Unknown';
            }
        }
        
        // Send message to chat
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            if (!message) return;
            
            // Add user message to chat
            addMessage(message, 'user');
            input.value = '';
            
            try {
                // Send to API
                const response = await fetch('http://localhost:8080/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: 'web_user',
                        message: message
                    })
                });
                
                const data = await response.json();
                addMessage(data.response || 'Sorry, I encountered an error.', 'assistant');
                
            } catch (error) {
                addMessage('Error: Could not connect to server. Please make sure ATLAS is running.', 'assistant');
            }
        }
        
        function addMessage(text, sender) {
            const messagesDiv = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;
            messageDiv.textContent = text;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }
        
        // Check status on load and every 5 seconds
        checkStatus();
        setInterval(checkStatus, 5000);
    </script>
</body>
</html>"""
        
        interface_file = self.project_root / 'atlas_interface.html'
        interface_file.write_text(html_content, encoding='utf-8')
        logger.info(f"Created web interface at {interface_file}")
        
    def run(self):
        """Main launcher sequence"""
        print("\n" + "="*50)
        print("🚀 ATLAS Trading Assistant Launcher")
        print("="*50 + "\n")
        
        # Check environment
        if not self.check_environment():
            logger.error("❌ Environment check failed. Please configure your API keys.")
            return False
            
        # Check dependencies
        if not self.check_dependencies():
            logger.error("❌ Dependency check failed. Please install required packages.")
            return False
            
        # Start server
        if not self.start_server():
            logger.error("❌ Failed to start server.")
            return False
            
        # Open interface
        self.open_interface()
        
        print("\n" + "="*50)
        print("✅ ATLAS is running!")
        print("="*50)
        print("\nAccess the web interface at: http://localhost:8080")
        print("Press Ctrl+C to stop the server\n")
        
        # Keep running
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("\nShutting down ATLAS...")
            if self.server_process:
                self.server_process.terminate()
                self.server_process.wait()
            logger.info("✅ ATLAS stopped successfully")
            
        return True

def main():
    launcher = ATLASLauncher()
    launcher.run()

if __name__ == "__main__":
    main() 