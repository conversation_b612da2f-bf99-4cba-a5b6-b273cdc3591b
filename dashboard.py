import os
import requests
import streamlit as st
import json
import logging
from datetime import datetime
import pandas as pd
from typing import Dict, List, Optional, Any
from chat_state_manager import ChatStateManager
from alpaca_integration import get_alpaca_account

# Configure console logging for chat monitoring
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - [ATLAS_CHAT] - %(message)s',
    handlers=[
        logging.StreamHandler(),  # Console output
        logging.FileHandler('logs/atlas_chat.log', encoding='utf-8')  # File logging
    ]
)
logger = logging.getLogger(__name__)
logger.info("🚀 ATLAS Enhanced Dashboard Started - With Live Trade Monitoring")

# Load configuration
PROXY_URL = os.getenv("PROXY_URL", "http://127.0.0.1:8080")
ALPACA_KEY = os.getenv("APCA_API_KEY_ID")
ALPACA_SECRET = os.getenv("APCA_API_SECRET_KEY")

st.set_page_config(page_title="ATLAS Trading Assistant", page_icon="📈", layout="wide")

# Initialize session state
if "chat_manager" not in st.session_state:
    st.session_state.chat_manager = ChatStateManager()
    st.session_state.messages = []
    st.session_state.user_id = f"user_{hash(str(datetime.now()))}"
    st.session_state.active_trades = {}
    st.session_state.trade_history = []
    st.session_state.ws_connected = False
    
    # Show initial greeting
    initial_greeting = (
        "👋 Hello! I'm ATLAS, your intelligent trading assistant.\n\n"
        "I can help you:\n"
        "• 📈 Find profitable trading opportunities\n"
        "• 💰 Execute trades to reach your profit goals\n"
        "• 📊 Monitor your live positions and P&L\n"
        "• 📚 Access any market data or fundamentals\n"
        "• 🛡️ Manage risk effectively\n\n"
        "Say **'start'** to begin setting up trades, or ask me anything about markets!"
    )
    st.session_state.messages.append({"role": "assistant", "content": initial_greeting})

# ============================================================================
# HELPER FUNCTIONS
# ============================================================================

def connect_websocket():
    """Connect to Alpaca WebSocket for real-time updates"""
    # This would connect to Alpaca's WebSocket for real-time position updates
    st.session_state.ws_connected = True
    st.success("Connected to live feed!")

def exit_position(symbol: str):
    """Exit a position"""
    with st.spinner(f"Closing position for {symbol}..."):
        response = requests.post(
            f"{PROXY_URL}/close_position",
            json={"symbol": symbol}
        )
        
        if response.status_code == 200:
            st.success(f"✅ Successfully closed {symbol} position!")
            st.rerun()
        else:
            st.error(f"Failed to close position: {response.text}")

def export_trade_history():
    """Export trade history to CSV"""
    alpaca = get_alpaca_account()
    orders = alpaca.get_orders(status="closed", limit=500)
    
    if orders:
        df = pd.DataFrame(orders)
        csv = df.to_csv(index=False)
        st.download_button(
            label="Download CSV",
            data=csv,
            file_name=f"atlas_trades_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            mime="text/csv"
        )

def handle_llm_actions(actions: Dict[str, Any]):
    """Handle actions from LLM responses"""
    if actions.get("execute_trade"):
        # Handle trade execution
        st.info("Executing trade based on your request...")
    elif actions.get("show_data"):
        # Display requested data
        st.info("Fetching requested data...")

def save_preferences(prefs: Dict[str, Any]):
    """Save user preferences"""
    # Save to session state or database
    st.session_state.preferences = prefs

def check_api_status(api: str) -> bool:
    """Check if an API is connected"""
    try:
        if api == "alpaca":
            response = requests.get(f"{PROXY_URL}/account/info")
        elif api == "fmp":
            response = requests.post(f"{PROXY_URL}/call_fmp", json={"path": "/profile/AAPL"})
        elif api == "openai":
            response = requests.post(f"{PROXY_URL}/chat", json={"user_id": "test", "message": "test"})
        else:
            return False
        
        return response.status_code == 200
    except:
        return False

# Create tabs
tab1, tab2, tab3, tab4 = st.tabs(["💬 Chat", "📊 Live Trades", "📈 Account", "⚙️ Settings"])

# ============================================================================
# TAB 1: CHAT INTERFACE
# ============================================================================
with tab1:
    st.title("📈 ATLAS Trading Assistant")

    # Render chat messages
    for msg in st.session_state.messages:
        avatar = "🤖" if msg["role"] == "assistant" else "🧑"
        with st.chat_message(msg["role"], avatar=avatar):
            st.markdown(msg["content"])
    
    # Chat input
    prompt = st.chat_input("Ask ATLAS anything about trading, markets, or financial data...")

    if prompt:
        # Log user message
        logger.info(f"👤 USER: {prompt}")
        
        # Add user message to display
        st.session_state.messages.append({"role": "user", "content": prompt})
        with st.chat_message("user", avatar="🧑"):
            st.markdown(prompt)
        
        # Process through enhanced LLM with generic API access
        with st.spinner("Thinking..."):
            response = requests.post(
                f"{PROXY_URL}/chat",
                json={
                    "user_id": st.session_state.user_id,
                    "message": prompt,
                    "context": {
                        "active_trades": list(st.session_state.active_trades.keys()),
                        "has_pending_trade": hasattr(st.session_state.chat_manager, 'pending_trade')
                    }
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                assistant_response = result.get("response", "I'm having trouble understanding that.")
                
                # Display response
                with st.chat_message("assistant", avatar="🤖"):
                    st.markdown(assistant_response)
                st.session_state.messages.append({"role": "assistant", "content": assistant_response})
                logger.info(f"🤖 ATLAS: {assistant_response[:200]}...")
                
                # Handle any actions (trades, data requests, etc.)
                if result.get("actions"):
                    handle_llm_actions(result["actions"])
            else:
                st.error(f"Error: {response.status_code}")

# ============================================================================
# TAB 2: LIVE TRADES MONITORING
# ============================================================================
with tab2:
    st.header("📊 Live Positions & P/L")
    
    # Connect to WebSocket for real-time updates
    if not st.session_state.ws_connected:
        if st.button("🔌 Connect Live Feed"):
            connect_websocket()
    
    # Get current positions from Alpaca
    alpaca = get_alpaca_account()
    positions = alpaca.get_positions()
    
    if positions:
        # Create DataFrame for positions
        positions_data = []
        for pos in positions:
            positions_data.append({
                "Symbol": pos["symbol"],
                "Quantity": pos["quantity"],
                "Side": pos["side"],
                "Entry Price": f"${pos['avg_entry_price']:.2f}",
                "Current Price": f"${pos['current_price']:.2f}",
                "Market Value": f"${pos['market_value']:.2f}",
                "Unrealized P/L": f"${pos['unrealized_pl']:.2f}",
                "P/L %": f"{pos['unrealized_plpc']:.2%}",
                "Today's P/L": f"${pos['unrealized_intraday_pl']:.2f}",
                "Action": pos["symbol"]  # For exit button
            })
        
        df = pd.DataFrame(positions_data)
        
        # Display positions with exit buttons
        for idx, row in df.iterrows():
            col1, col2, col3, col4, col5, col6, col7, col8, col9 = st.columns([1, 1, 1, 1.2, 1.2, 1.2, 1, 1, 1])
            
            col1.write(row["Symbol"])
            col2.write(row["Quantity"])
            col3.write(row["Entry Price"])
            col4.write(row["Current Price"])
            col5.write(row["Market Value"])
            
            # Color code P/L
            pl_value = float(row["Unrealized P/L"].replace("$", "").replace(",", ""))
            if pl_value >= 0:
                col6.success(row["Unrealized P/L"])
                col7.success(row["P/L %"])
            else:
                col6.error(row["Unrealized P/L"])
                col7.error(row["P/L %"])
            
            col8.write(row["Today's P/L"])
            
            # Exit button
            if col9.button("Exit", key=f"exit_{row['Symbol']}_{idx}"):
                exit_position(row["Symbol"])
        
        # Summary metrics
        st.markdown("---")
        col1, col2, col3, col4 = st.columns(4)
        
        total_value = sum(pos["market_value"] for pos in positions)
        total_pl = sum(pos["unrealized_pl"] for pos in positions)
        total_pl_pct = (total_pl / total_value * 100) if total_value > 0 else 0
        
        col1.metric("Total Positions", len(positions))
        col2.metric("Total Value", f"${total_value:,.2f}")
        col3.metric("Total Unrealized P/L", f"${total_pl:,.2f}", f"{total_pl_pct:.2f}%")
        col4.metric("Today's P/L", f"${sum(pos['unrealized_intraday_pl'] for pos in positions):,.2f}")
        
    else:
        st.info("No active positions. Start trading to see your positions here!")
    
    # Trade History
    st.markdown("---")
    st.subheader("📜 Trade History")
    
    if st.button("Export Trade History"):
        export_trade_history()
    
    # Get recent closed orders
    orders = alpaca.get_orders(status="closed", limit=20)
    if orders:
        history_data = []
        for order in orders:
            if order["status"] == "filled":
                history_data.append({
                    "Time": order["filled_at"][:19] if order["filled_at"] else order["created_at"][:19],
                    "Symbol": order["symbol"],
                    "Side": order["side"],
                    "Quantity": order["filled_qty"],
                    "Price": f"${order['filled_avg_price']:.2f}" if order["filled_avg_price"] else "N/A",
                    "Type": order["type"],
                    "Status": order["status"]
                })
        
        if history_data:
            st.dataframe(pd.DataFrame(history_data), use_container_width=True)

# ============================================================================
# TAB 3: ACCOUNT OVERVIEW
# ============================================================================
with tab3:
    st.header("📈 Account Overview")
    
    # Get account summary
    account_summary = alpaca.get_account_summary()
    
    if "error" not in account_summary:
        # Account metrics
        col1, col2, col3, col4 = st.columns(4)
        
        account = account_summary["account"]
        col1.metric("Portfolio Value", f"${account['portfolio_value']:,.2f}")
        col2.metric("Buying Power", f"${account['buying_power']:,.2f}")
        col3.metric("Cash", f"${account['cash']:,.2f}")
        col4.metric("Day Trades", f"{account['day_trade_count']}/3")
        
        # Portfolio performance chart
        st.subheader("Portfolio Performance")
        
        history = account_summary["performance"]["portfolio_history"]
        if "history" in history and history["history"]:
            df_history = pd.DataFrame(history["history"])
            st.line_chart(df_history.set_index("timestamp")["equity"])
        
        # Market status
        st.subheader("Market Status")
        market = account_summary["market_status"]
        if market["is_open"]:
            st.success("🟢 Market is OPEN")
        else:
            st.warning("🔴 Market is CLOSED")
            if market.get("next_open"):
                st.info(f"Next open: {market['next_open']}")
    else:
        st.error(account_summary["error"])

# ============================================================================
# TAB 4: SETTINGS
# ============================================================================
with tab4:
    st.header("⚙️ Settings")
    
    # Trading preferences
    st.subheader("Trading Preferences")
    
    col1, col2 = st.columns(2)
    with col1:
        capital = st.number_input("Available Capital ($)", value=30000, step=1000)
        max_loss_per_trade = st.slider("Max Loss per Trade (%)", 1, 10, 2)
        
    with col2:
        preferred_markets = st.multiselect(
            "Preferred Markets",
            ["Stocks", "Options", "Crypto", "Forex"],
            default=["Stocks"]
        )
        risk_level = st.select_slider(
            "Risk Tolerance",
            options=["Conservative", "Moderate", "Aggressive"],
            value="Moderate"
        )
    
    # Save preferences
    if st.button("Save Preferences"):
        save_preferences({
            "capital": capital,
            "max_loss_per_trade": max_loss_per_trade,
            "preferred_markets": preferred_markets,
            "risk_level": risk_level
        })
        st.success("Preferences saved!")
    
    # API Status
    st.subheader("API Status")
    col1, col2, col3 = st.columns(3)
    
    # Check Alpaca connection
    alpaca_status = check_api_status("alpaca")
    col1.metric("Alpaca API", "✅ Connected" if alpaca_status else "❌ Disconnected")
    
    # Check FMP connection
    fmp_status = check_api_status("fmp")
    col2.metric("FMP API", "✅ Connected" if fmp_status else "❌ Disconnected")
    
    # Check OpenAI connection
    openai_status = check_api_status("openai")
    col3.metric("OpenAI API", "✅ Connected" if openai_status else "❌ Disconnected")
    
    # Reset button
    st.markdown("---")
    if st.button("🔄 Reset Conversation", type="secondary"):
        st.session_state.chat_manager.reset()
        st.session_state.messages = [{
            "role": "assistant", 
            "content": "Conversation reset! Say 'start' to begin setting up new trades, or ask me anything."
        }]
        st.rerun()

# Run the dashboard
if __name__ == "__main__":
    # The Streamlit app runs automatically
    pass 