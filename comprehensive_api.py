"""
ATLAS v2 - Comprehensive API Integration
Complete access to ALL Financial Modeling Prep and Alpaca endpoints
"""

import os
import requests
import json
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
import alpaca_trade_api as tradeapi
from alpaca.trading.client import TradingClient
from alpaca.trading.requests import *
from alpaca.trading.enums import *
from alpaca.data.historical import StockHistoricalDataClient, CryptoHistoricalDataClient
from alpaca.data.requests import *
from alpaca.data.live import StockDataStream, CryptoDataStream
import logging

logger = logging.getLogger(__name__)

class ComprehensiveAPI:
    """Complete API integration for ATLAS with ALL endpoints"""
    
    def __init__(self):
        # FMP Configuration
        self.fmp_api_key = "your_fmp_key"  # Replace with actual key if you have one
        self.fmp_base_url = "https://financialmodelingprep.com/api"
        
        # Alpaca Configuration
        self.alpaca_api_key = "PKQHL5QJEGSZWD30TW9D"
        self.alpaca_secret_key = "8EdGvvdTt8jGY0Xcw5WZMUqPjoVbyEVvhKokZuE1"
        self.alpaca_paper = True  # Using paper trading
        
        # Initialize Alpaca clients
        self.trading_client = TradingClient(
            self.alpaca_api_key,
            self.alpaca_secret_key,
            paper=self.alpaca_paper
        )
        
        self.stock_data_client = StockHistoricalDataClient(
            self.alpaca_api_key,
            self.alpaca_secret_key
        )
        
        self.crypto_data_client = CryptoHistoricalDataClient(
            self.alpaca_api_key,
            self.alpaca_secret_key
        )
        
        # Legacy REST client for compatibility
        self.alpaca_rest = tradeapi.REST(
            self.alpaca_api_key,
            self.alpaca_secret_key,
            base_url='https://paper-api.alpaca.markets' if self.alpaca_paper else 'https://api.alpaca.markets'
        )
        
        logger.info("✅ Comprehensive API initialized with full access to FMP and Alpaca")
    
    # ==================== FINANCIAL MODELING PREP API ====================
    
    def fmp_request(self, endpoint: str, params: Dict = None) -> Optional[Any]:
        """Make a request to FMP API"""
        if not self.fmp_api_key:
            return {"error": "FMP API key not configured"}
        
        url = f"{self.fmp_base_url}{endpoint}"
        if params is None:
            params = {}
        params['apikey'] = self.fmp_api_key
        
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"FMP API error: {e}")
            return {"error": str(e)}
    
    # === FMP Search Endpoints ===
    def fmp_search_symbol(self, query: str) -> List[Dict]:
        """Search for stock symbols"""
        return self.fmp_request("/v3/search", {"query": query})
    
    def fmp_search_name(self, query: str) -> List[Dict]:
        """Search by company name"""
        return self.fmp_request("/v3/search-name", {"query": query})
    
    def fmp_search_cik(self, cik: str) -> Dict:
        """Search by CIK"""
        return self.fmp_request("/v3/search-cik", {"cik": cik})
    
    def fmp_search_cusip(self, cusip: str) -> Dict:
        """Search by CUSIP"""
        return self.fmp_request("/v3/search-cusip", {"cusip": cusip})
    
    def fmp_search_isin(self, isin: str) -> Dict:
        """Search by ISIN"""
        return self.fmp_request("/v3/search-isin", {"isin": isin})
    
    # === FMP Company Data ===
    def fmp_company_profile(self, symbol: str) -> Dict:
        """Get company profile"""
        return self.fmp_request(f"/v3/profile/{symbol}")
    
    def fmp_key_executives(self, symbol: str) -> List[Dict]:
        """Get key executives"""
        return self.fmp_request(f"/v3/key-executives/{symbol}")
    
    def fmp_company_outlook(self, symbol: str) -> Dict:
        """Get comprehensive company outlook"""
        return self.fmp_request(f"/v4/company-outlook", {"symbol": symbol})
    
    # === FMP Financial Statements ===
    def fmp_income_statement(self, symbol: str, period: str = "annual", limit: int = 10) -> List[Dict]:
        """Get income statements"""
        return self.fmp_request(f"/v3/income-statement/{symbol}", {"period": period, "limit": limit})
    
    def fmp_balance_sheet(self, symbol: str, period: str = "annual", limit: int = 10) -> List[Dict]:
        """Get balance sheet statements"""
        return self.fmp_request(f"/v3/balance-sheet-statement/{symbol}", {"period": period, "limit": limit})
    
    def fmp_cash_flow(self, symbol: str, period: str = "annual", limit: int = 10) -> List[Dict]:
        """Get cash flow statements"""
        return self.fmp_request(f"/v3/cash-flow-statement/{symbol}", {"period": period, "limit": limit})
    
    # === FMP Insider Trading ===
    def fmp_insider_trading(self, symbol: str = None, page: int = 0) -> List[Dict]:
        """Get insider trading data"""
        endpoint = "/v4/insider-trading"
        params = {"page": page}
        if symbol:
            params["symbol"] = symbol
        return self.fmp_request(endpoint, params)
    
    def fmp_insider_trading_rss(self, page: int = 0) -> List[Dict]:
        """Get latest insider trading RSS feed"""
        return self.fmp_request("/v4/insider-trading-rss-feed", {"page": page})
    
    # === FMP Senate/House Trading ===
    def fmp_senate_trading(self, symbol: str = None) -> List[Dict]:
        """Get Senate trading data"""
        endpoint = "/v4/senate-trading" if symbol else "/v4/senate-trading-rss-feed"
        params = {"symbol": symbol} if symbol else {}
        return self.fmp_request(endpoint, params)
    
    def fmp_house_trading(self, symbol: str = None) -> List[Dict]:
        """Get House trading data"""
        endpoint = "/v4/house-disclosure" if symbol else "/v4/house-disclosure-rss-feed"
        params = {"symbol": symbol} if symbol else {}
        return self.fmp_request(endpoint, params)
    
    # === FMP Analyst Estimates ===
    def fmp_analyst_estimates(self, symbol: str, period: str = "annual", limit: int = 10) -> List[Dict]:
        """Get analyst estimates"""
        return self.fmp_request(f"/v3/analyst-estimates/{symbol}", {"period": period, "limit": limit})
    
    def fmp_analyst_recommendations(self, symbol: str) -> List[Dict]:
        """Get analyst recommendations"""
        return self.fmp_request(f"/v3/analyst-stock-recommendations/{symbol}")
    
    def fmp_price_target(self, symbol: str) -> Dict:
        """Get price target consensus"""
        return self.fmp_request(f"/v4/price-target-consensus", {"symbol": symbol})
    
    def fmp_price_target_summary(self, symbol: str) -> Dict:
        """Get price target summary"""
        return self.fmp_request(f"/v4/price-target-summary", {"symbol": symbol})
    
    # === FMP Market Data ===
    def fmp_quote(self, symbol: str) -> Dict:
        """Get real-time quote"""
        return self.fmp_request(f"/v3/quote/{symbol}")
    
    def fmp_quote_short(self, symbol: str) -> Dict:
        """Get short quote"""
        return self.fmp_request(f"/v3/quote-short/{symbol}")
    
    def fmp_historical_price(self, symbol: str, from_date: str = None, to_date: str = None) -> List[Dict]:
        """Get historical prices"""
        params = {}
        if from_date:
            params["from"] = from_date
        if to_date:
            params["to"] = to_date
        return self.fmp_request(f"/v3/historical-price-full/{symbol}", params)
    
    # === FMP Technical Indicators ===
    def fmp_technical_indicator(self, symbol: str, indicator: str, interval: str = "daily", period: int = 10) -> List[Dict]:
        """Get technical indicators (SMA, EMA, RSI, MACD, etc.)"""
        return self.fmp_request(f"/v3/technical_indicator/{interval}/{symbol}", 
                               {"type": indicator, "period": period})
    
    # === FMP Screener ===
    def fmp_stock_screener(self, **kwargs) -> List[Dict]:
        """
        Stock screener with multiple filters:
        - marketCapMoreThan, marketCapLowerThan
        - priceMoreThan, priceLowerThan
        - betaMoreThan, betaLowerThan
        - volumeMoreThan, volumeLowerThan
        - dividendMoreThan, dividendLowerThan
        - isEtf, isActivelyTrading
        - sector, industry, country, exchange
        - limit
        """
        return self.fmp_request("/v3/stock-screener", kwargs)
    
    # === FMP Calendar ===
    def fmp_earnings_calendar(self, from_date: str = None, to_date: str = None) -> List[Dict]:
        """Get earnings calendar"""
        params = {}
        if from_date:
            params["from"] = from_date
        if to_date:
            params["to"] = to_date
        return self.fmp_request("/v3/earning_calendar", params)
    
    def fmp_ipo_calendar(self, from_date: str = None, to_date: str = None) -> List[Dict]:
        """Get IPO calendar"""
        params = {}
        if from_date:
            params["from"] = from_date
        if to_date:
            params["to"] = to_date
        return self.fmp_request("/v3/ipo_calendar", params)
    
    def fmp_economic_calendar(self, from_date: str = None, to_date: str = None) -> List[Dict]:
        """Get economic calendar"""
        params = {}
        if from_date:
            params["from"] = from_date
        if to_date:
            params["to"] = to_date
        return self.fmp_request("/v3/economic_calendar", params)
    
    # === FMP News ===
    def fmp_stock_news(self, symbol: str = None, limit: int = 50) -> List[Dict]:
        """Get stock news"""
        endpoint = f"/v3/stock_news"
        params = {"limit": limit}
        if symbol:
            params["tickers"] = symbol
        return self.fmp_request(endpoint, params)
    
    def fmp_press_releases(self, symbol: str, page: int = 0) -> List[Dict]:
        """Get press releases"""
        return self.fmp_request(f"/v3/press-releases/{symbol}", {"page": page})
    
    # === FMP Crypto ===
    def fmp_crypto_quote(self, symbol: str) -> Dict:
        """Get crypto quote"""
        return self.fmp_request(f"/v3/quote/{symbol}")
    
    def fmp_crypto_historical(self, symbol: str, interval: str = "1hour") -> List[Dict]:
        """Get crypto historical data"""
        return self.fmp_request(f"/v3/historical-chart/{interval}/{symbol}")
    
    # === FMP ETF Data ===
    def fmp_etf_holders(self, symbol: str) -> List[Dict]:
        """Get ETF holders"""
        return self.fmp_request(f"/v3/etf-holder/{symbol}")
    
    def fmp_etf_sector_weightings(self, symbol: str) -> List[Dict]:
        """Get ETF sector weightings"""
        return self.fmp_request(f"/v3/etf-sector-weightings/{symbol}")
    
    def fmp_etf_country_weightings(self, symbol: str) -> List[Dict]:
        """Get ETF country weightings"""
        return self.fmp_request(f"/v3/etf-country-weightings/{symbol}")
    
    # === FMP Form 13F ===
    def fmp_form_13f(self, cik: str, date: str = None) -> List[Dict]:
        """Get Form 13F data"""
        params = {}
        if date:
            params["date"] = date
        return self.fmp_request(f"/v3/form-thirteen/{cik}", params)
    
    def fmp_form_13f_dates(self, cik: str) -> List[str]:
        """Get available Form 13F dates"""
        return self.fmp_request(f"/v3/form-thirteen-date/{cik}")
    
    # === FMP Ratios & Metrics ===
    def fmp_ratios(self, symbol: str, period: str = "annual", limit: int = 10) -> List[Dict]:
        """Get financial ratios"""
        return self.fmp_request(f"/v3/ratios/{symbol}", {"period": period, "limit": limit})
    
    def fmp_key_metrics(self, symbol: str, period: str = "annual", limit: int = 10) -> List[Dict]:
        """Get key metrics"""
        return self.fmp_request(f"/v3/key-metrics/{symbol}", {"period": period, "limit": limit})
    
    def fmp_financial_growth(self, symbol: str, period: str = "annual", limit: int = 10) -> List[Dict]:
        """Get financial growth metrics"""
        return self.fmp_request(f"/v3/financial-growth/{symbol}", {"period": period, "limit": limit})
    
    def fmp_rating(self, symbol: str) -> Dict:
        """Get stock rating"""
        return self.fmp_request(f"/v3/rating/{symbol}")
    
    def fmp_dcf(self, symbol: str) -> Dict:
        """Get DCF valuation"""
        return self.fmp_request(f"/v3/discounted-cash-flow/{symbol}")
    
    # === FMP Lists & Screeners ===
    def fmp_company_screener(self, **kwargs) -> List[Dict]:
        """Company screener with filters"""
        return self.fmp_request("/v3/stock-screener", kwargs)
    
    def fmp_stock_list(self) -> List[Dict]:
        """Get all available stocks"""
        return self.fmp_request("/v3/stock/list")
    
    def fmp_financial_statement_symbol_list(self) -> List[Dict]:
        """Get list of symbols with financial statements"""
        return self.fmp_request("/v3/financial-statement-symbol-lists")
    
    def fmp_cik_list(self) -> List[Dict]:
        """Get CIK list"""
        return self.fmp_request("/v3/cik_list")
    
    def fmp_symbol_change(self) -> List[Dict]:
        """Get symbol changes"""
        return self.fmp_request("/v4/symbol_change")
    
    def fmp_etf_list(self) -> List[Dict]:
        """Get ETF list"""
        return self.fmp_request("/v3/etf/list")
    
    def fmp_actively_trading_list(self) -> List[Dict]:
        """Get actively trading stocks"""
        return self.fmp_request("/v3/available-traded/list")
    
    def fmp_earnings_transcript_list(self) -> List[Dict]:
        """Get earnings transcript list"""
        return self.fmp_request("/v4/earning_call_transcript")
    
    def fmp_available_exchanges(self) -> List[str]:
        """Get available exchanges"""
        return self.fmp_request("/v3/exchanges-list")
    
    def fmp_available_sectors(self) -> List[str]:
        """Get available sectors"""
        return self.fmp_request("/v3/sectors-list")
    
    def fmp_available_industries(self) -> List[str]:
        """Get available industries"""
        return self.fmp_request("/v3/industries-list")
    
    def fmp_available_countries(self) -> List[str]:
        """Get available countries"""
        return self.fmp_request("/v3/get-all-countries")
    
    # === FMP Ratings & Grades ===
    def fmp_ratings_snapshot(self, symbol: str) -> Dict:
        """Get ratings snapshot"""
        return self.fmp_request(f"/v3/rating/{symbol}")
    
    def fmp_ratings_historical(self, symbol: str, limit: int = 100) -> List[Dict]:
        """Get historical ratings"""
        return self.fmp_request(f"/v3/historical-rating/{symbol}", {"limit": limit})
    
    def fmp_grades(self, symbol: str) -> List[Dict]:
        """Get analyst grades"""
        return self.fmp_request(f"/v3/grade/{symbol}")
    
    def fmp_grades_historical(self, symbol: str, limit: int = 100) -> List[Dict]:
        """Get historical grades"""
        return self.fmp_request(f"/v3/historical-grade/{symbol}", {"limit": limit})
    
    def fmp_grades_consensus(self, symbol: str) -> Dict:
        """Get grades consensus"""
        return self.fmp_request(f"/v4/upgrades-downgrades-consensus", {"symbol": symbol})
    
    def fmp_grades_news(self, symbol: str, page: int = 0, limit: int = 10) -> List[Dict]:
        """Get grades news"""
        return self.fmp_request(f"/v4/upgrades-downgrades-grading-search", 
                               {"symbol": symbol, "page": page, "limit": limit})
    
    def fmp_grades_latest_news(self, page: int = 0, limit: int = 10) -> List[Dict]:
        """Get latest grades news"""
        return self.fmp_request("/v4/upgrades-downgrades-rss-feed", {"page": page, "limit": limit})
    
    # === FMP Calendar Events ===
    def fmp_dividends(self, symbol: str) -> List[Dict]:
        """Get dividend history"""
        return self.fmp_request(f"/v3/historical-price-full/stock_dividend/{symbol}")
    
    def fmp_dividends_calendar(self, from_date: str = None, to_date: str = None) -> List[Dict]:
        """Get dividend calendar"""
        params = {}
        if from_date:
            params["from"] = from_date
        if to_date:
            params["to"] = to_date
        return self.fmp_request("/v3/stock_dividend_calendar", params)
    
    def fmp_earnings(self, symbol: str) -> List[Dict]:
        """Get earnings history"""
        return self.fmp_request(f"/v3/historical/earning_calendar/{symbol}")
    
    def fmp_ipos_disclosure(self) -> List[Dict]:
        """Get IPO disclosure"""
        return self.fmp_request("/v4/ipo-calendar-prospectus")
    
    def fmp_ipos_prospectus(self) -> List[Dict]:
        """Get IPO prospectus"""
        return self.fmp_request("/v4/ipo-calendar-prospectus")
    
    def fmp_splits(self, symbol: str) -> List[Dict]:
        """Get stock splits"""
        return self.fmp_request(f"/v3/historical-price-full/stock_split/{symbol}")
    
    def fmp_splits_calendar(self, from_date: str = None, to_date: str = None) -> List[Dict]:
        """Get splits calendar"""
        params = {}
        if from_date:
            params["from"] = from_date
        if to_date:
            params["to"] = to_date
        return self.fmp_request("/v3/stock_split_calendar", params)
    
    # === FMP Historical Price Data ===
    def fmp_historical_price_eod_light(self, symbol: str) -> List[Dict]:
        """Get light EOD historical prices"""
        return self.fmp_request(f"/v3/historical-price-full/{symbol}")
    
    def fmp_historical_price_eod_full(self, symbol: str, from_date: str = None, to_date: str = None) -> List[Dict]:
        """Get full EOD historical prices"""
        params = {}
        if from_date:
            params["from"] = from_date
        if to_date:
            params["to"] = to_date
        return self.fmp_request(f"/v3/historical-price-full/{symbol}", params)
    
    def fmp_historical_price_non_split_adjusted(self, symbol: str) -> List[Dict]:
        """Get non-split adjusted prices"""
        return self.fmp_request(f"/v3/historical-price-full/{symbol}", {"datatype": "line"})
    
    def fmp_historical_price_dividend_adjusted(self, symbol: str) -> List[Dict]:
        """Get dividend adjusted prices"""
        return self.fmp_request(f"/v3/historical-price-full/{symbol}", {"datatype": "line"})
    
    def fmp_historical_chart(self, symbol: str, interval: str = "1hour") -> List[Dict]:
        """Get historical chart data (1min, 5min, 15min, 30min, 1hour, 4hour)"""
        return self.fmp_request(f"/v3/historical-chart/{interval}/{symbol}")
    
    # === FMP Company Information ===
    def fmp_profile_cik(self, cik: str) -> Dict:
        """Get company profile by CIK"""
        return self.fmp_request(f"/v3/profile/cik/{cik}")
    
    def fmp_company_notes(self, symbol: str) -> Dict:
        """Get company notes"""
        return self.fmp_request(f"/v4/company-notes", {"symbol": symbol})
    
    def fmp_stock_peers(self, symbol: str) -> List[str]:
        """Get stock peers"""
        return self.fmp_request(f"/v4/stock_peers", {"symbol": symbol})
    
    def fmp_delisted_companies(self, page: int = 0, limit: int = 100) -> List[Dict]:
        """Get delisted companies"""
        return self.fmp_request("/v3/delisted-companies", {"page": page, "limit": limit})
    
    def fmp_employee_count(self, symbol: str) -> Dict:
        """Get employee count"""
        return self.fmp_request(f"/v4/employee_count", {"symbol": symbol})
    
    def fmp_historical_employee_count(self, symbol: str) -> List[Dict]:
        """Get historical employee count"""
        return self.fmp_request(f"/v4/historical/employee_count", {"symbol": symbol})
    
    def fmp_market_capitalization(self, symbol: str) -> Dict:
        """Get market capitalization"""
        return self.fmp_request(f"/v3/market-capitalization/{symbol}")
    
    def fmp_market_capitalization_batch(self, symbols: List[str]) -> List[Dict]:
        """Get market cap for multiple symbols"""
        return self.fmp_request("/v3/market-capitalization", {"symbols": ",".join(symbols)})
    
    def fmp_historical_market_capitalization(self, symbol: str, limit: int = 100) -> List[Dict]:
        """Get historical market cap"""
        return self.fmp_request(f"/v3/historical-market-capitalization/{symbol}", {"limit": limit})
    
    def fmp_shares_float(self, symbol: str) -> Dict:
        """Get shares float"""
        return self.fmp_request(f"/v4/shares_float", {"symbol": symbol})
    
    def fmp_shares_float_all(self, page: int = 0, limit: int = 1000) -> List[Dict]:
        """Get all shares float"""
        return self.fmp_request("/v4/shares_float/all", {"page": page, "limit": limit})
    
    # === FMP M&A and Executive Data ===
    def fmp_mergers_acquisitions_latest(self, page: int = 0, limit: int = 100) -> List[Dict]:
        """Get latest M&A deals"""
        return self.fmp_request("/v4/mergers-acquisitions-rss-feed", {"page": page, "limit": limit})
    
    def fmp_mergers_acquisitions_search(self, name: str) -> List[Dict]:
        """Search M&A deals"""
        return self.fmp_request("/v4/mergers-acquisitions/search", {"name": name})
    
    def fmp_governance_executive_compensation(self, symbol: str) -> List[Dict]:
        """Get executive compensation"""
        return self.fmp_request(f"/v4/governance/executive_compensation", {"symbol": symbol})
    
    def fmp_executive_compensation_benchmark(self, year: int = None) -> List[Dict]:
        """Get executive compensation benchmark"""
        params = {}
        if year:
            params["year"] = year
        return self.fmp_request("/v4/executive-compensation-benchmark", params)
    
    # === FMP Commitment of Traders ===
    def fmp_commitment_of_traders_report(self) -> List[Dict]:
        """Get COT report"""
        return self.fmp_request("/v4/commitment_of_traders_report")
    
    def fmp_commitment_of_traders_analysis(self) -> List[Dict]:
        """Get COT analysis"""
        return self.fmp_request("/v4/commitment_of_traders_report_analysis")
    
    def fmp_commitment_of_traders_list(self) -> List[Dict]:
        """Get COT list"""
        return self.fmp_request("/v4/commitment_of_traders_report/list")
    
    # === FMP Valuation ===
    def fmp_levered_dcf(self, symbol: str) -> Dict:
        """Get levered DCF"""
        return self.fmp_request(f"/v4/advanced_levered_discounted_cash_flow", {"symbol": symbol})
    
    def fmp_custom_dcf(self, symbol: str, **kwargs) -> Dict:
        """Get custom DCF with parameters"""
        params = {"symbol": symbol}
        params.update(kwargs)
        return self.fmp_request("/v4/advanced_discounted_cash_flow", params)
    
    def fmp_custom_levered_dcf(self, symbol: str, **kwargs) -> Dict:
        """Get custom levered DCF"""
        params = {"symbol": symbol}
        params.update(kwargs)
        return self.fmp_request("/v4/advanced_levered_discounted_cash_flow", params)
    
    # === FMP Economic Data ===
    def fmp_treasury_rates(self) -> Dict:
        """Get treasury rates"""
        return self.fmp_request("/v4/treasury")
    
    def fmp_economic_indicators(self, name: str) -> List[Dict]:
        """Get economic indicators (GDP, CPI, etc.)"""
        return self.fmp_request(f"/v4/economic", {"name": name})
    
    def fmp_market_risk_premium(self) -> Dict:
        """Get market risk premium"""
        return self.fmp_request("/v4/market_risk_premium")
    
    # === FMP ESG ===
    def fmp_esg_disclosures(self, symbol: str) -> Dict:
        """Get ESG disclosures"""
        return self.fmp_request(f"/v4/esg-environmental-social-governance-data", {"symbol": symbol})
    
    def fmp_esg_ratings(self, symbol: str) -> Dict:
        """Get ESG ratings"""
        return self.fmp_request(f"/v4/esg-environmental-social-governance-data-ratings", {"symbol": symbol})
    
    def fmp_esg_benchmark(self, year: int = None) -> List[Dict]:
        """Get ESG benchmark"""
        params = {}
        if year:
            params["year"] = year
        return self.fmp_request("/v4/esg-environmental-social-governance-sector-benchmark", params)
    
    # === FMP ETF Extended ===
    def fmp_etf_holdings(self, symbol: str) -> List[Dict]:
        """Get ETF holdings"""
        return self.fmp_request(f"/v3/etf-holder/{symbol}")
    
    def fmp_etf_info(self, symbol: str) -> Dict:
        """Get ETF information"""
        return self.fmp_request(f"/v4/etf-info", {"symbol": symbol})
    
    def fmp_etf_asset_exposure(self, symbol: str) -> List[Dict]:
        """Get ETF asset exposure"""
        return self.fmp_request(f"/v3/etf-stock-exposure/{symbol}")
    
    # === FMP Funds ===
    def fmp_funds_disclosure_holders_latest(self, symbol: str) -> List[Dict]:
        """Get latest fund holders"""
        return self.fmp_request("/v4/mutual-fund-holdings/name", {"symbol": symbol})
    
    def fmp_funds_disclosure(self, symbol: str, year: int, quarter: int) -> List[Dict]:
        """Get fund disclosure"""
        return self.fmp_request(f"/v4/mutual-fund-holdings/portfolio-date", 
                               {"symbol": symbol, "date": f"{year}-Q{quarter}"})
    
    def fmp_funds_disclosure_holders_search(self, name: str) -> List[Dict]:
        """Search fund holders"""
        return self.fmp_request("/v4/mutual-fund-holdings/name", {"name": name})
    
    def fmp_funds_disclosure_dates(self, symbol: str) -> List[str]:
        """Get fund disclosure dates"""
        return self.fmp_request(f"/v4/mutual-fund-holdings/portfolio-date", {"symbol": symbol})
    
    # === FMP Commodities ===
    def fmp_commodities_list(self) -> List[Dict]:
        """Get commodities list"""
        return self.fmp_request("/v3/symbol/available-commodities")
    
    def fmp_batch_commodity_quotes(self) -> List[Dict]:
        """Get batch commodity quotes"""
        return self.fmp_request("/v3/quotes/commodity")
    
    # === FMP Crowdfunding ===
    def fmp_crowdfunding_offerings_latest(self, page: int = 0, limit: int = 100) -> List[Dict]:
        """Get latest crowdfunding offerings"""
        return self.fmp_request("/v4/crowdfunding-offerings-rss-feed", {"page": page, "limit": limit})
    
    def fmp_crowdfunding_offerings_search(self, name: str) -> List[Dict]:
        """Search crowdfunding offerings"""
        return self.fmp_request("/v4/crowdfunding-offerings/search", {"name": name})
    
    def fmp_crowdfunding_offerings(self, cik: str) -> List[Dict]:
        """Get crowdfunding offerings by CIK"""
        return self.fmp_request(f"/v4/crowdfunding-offerings", {"cik": cik})
    
    # === FMP Fundraising ===
    def fmp_fundraising_latest(self, page: int = 0, limit: int = 10) -> List[Dict]:
        """Get latest fundraising"""
        return self.fmp_request("/v4/fundraising-rss-feed", {"page": page, "limit": limit})
    
    def fmp_fundraising_search(self, name: str) -> List[Dict]:
        """Search fundraising"""
        return self.fmp_request("/v4/fundraising/search", {"name": name})
    
    def fmp_fundraising(self, cik: str) -> List[Dict]:
        """Get fundraising by CIK"""
        return self.fmp_request(f"/v4/fundraising", {"cik": cik})
    
    # === FMP Cryptocurrency ===
    def fmp_cryptocurrency_list(self) -> List[Dict]:
        """Get cryptocurrency list"""
        return self.fmp_request("/v3/symbol/available-cryptocurrencies")
    
    def fmp_batch_crypto_quotes(self) -> List[Dict]:
        """Get batch crypto quotes"""
        return self.fmp_request("/v3/quotes/crypto")
    
    # === FMP Forex ===
    def fmp_forex_list(self) -> List[Dict]:
        """Get forex pairs list"""
        return self.fmp_request("/v3/symbol/available-forex-currency-pairs")
    
    def fmp_batch_forex_quotes(self) -> List[Dict]:
        """Get batch forex quotes"""
        return self.fmp_request("/v3/quotes/forex")
    
    # === FMP Financial Statements Extended ===
    def fmp_latest_financial_statements(self, page: int = 0, limit: int = 250) -> List[Dict]:
        """Get latest financial statements"""
        return self.fmp_request("/v4/financial-reports-dates", {"page": page, "limit": limit})
    
    def fmp_income_statement_ttm(self, symbol: str) -> Dict:
        """Get TTM income statement"""
        return self.fmp_request(f"/v3/income-statement/{symbol}", {"period": "quarter", "limit": 1})
    
    def fmp_balance_sheet_ttm(self, symbol: str) -> Dict:
        """Get TTM balance sheet"""
        return self.fmp_request(f"/v3/balance-sheet-statement/{symbol}", {"period": "quarter", "limit": 1})
    
    def fmp_cash_flow_ttm(self, symbol: str) -> Dict:
        """Get TTM cash flow"""
        return self.fmp_request(f"/v3/cash-flow-statement/{symbol}", {"period": "quarter", "limit": 1})
    
    def fmp_key_metrics_ttm(self, symbol: str) -> Dict:
        """Get TTM key metrics"""
        return self.fmp_request(f"/v3/key-metrics-ttm/{symbol}")
    
    def fmp_ratios_ttm(self, symbol: str) -> Dict:
        """Get TTM ratios"""
        return self.fmp_request(f"/v3/ratios-ttm/{symbol}")
    
    def fmp_financial_scores(self, symbol: str) -> Dict:
        """Get financial scores"""
        return self.fmp_request(f"/v4/score", {"symbol": symbol})
    
    def fmp_owner_earnings(self, symbol: str) -> List[Dict]:
        """Get owner earnings"""
        return self.fmp_request(f"/v4/owner_earnings", {"symbol": symbol})
    
    def fmp_enterprise_values(self, symbol: str, period: str = "annual", limit: int = 10) -> List[Dict]:
        """Get enterprise values"""
        return self.fmp_request(f"/v3/enterprise-values/{symbol}", {"period": period, "limit": limit})
    
    def fmp_income_statement_growth(self, symbol: str, period: str = "annual", limit: int = 10) -> List[Dict]:
        """Get income statement growth"""
        return self.fmp_request(f"/v3/income-statement-growth/{symbol}", {"period": period, "limit": limit})
    
    def fmp_balance_sheet_growth(self, symbol: str, period: str = "annual", limit: int = 10) -> List[Dict]:
        """Get balance sheet growth"""
        return self.fmp_request(f"/v3/balance-sheet-statement-growth/{symbol}", {"period": period, "limit": limit})
    
    def fmp_cash_flow_growth(self, symbol: str, period: str = "annual", limit: int = 10) -> List[Dict]:
        """Get cash flow growth"""
        return self.fmp_request(f"/v3/cash-flow-statement-growth/{symbol}", {"period": period, "limit": limit})
    
    def fmp_financial_reports_dates(self, symbol: str) -> List[Dict]:
        """Get financial report dates"""
        return self.fmp_request(f"/v4/financial-reports-dates", {"symbol": symbol})
    
    def fmp_financial_reports_json(self, symbol: str, year: int, period: str = "FY") -> Dict:
        """Get financial reports in JSON"""
        return self.fmp_request(f"/v4/financial-reports-json", 
                               {"symbol": symbol, "year": year, "period": period})
    
    def fmp_financial_reports_xlsx(self, symbol: str, year: int, period: str = "FY") -> bytes:
        """Get financial reports in XLSX format"""
        # This returns binary data, needs special handling
        return self.fmp_request(f"/v4/financial-reports-xlsx", 
                               {"symbol": symbol, "year": year, "period": period})
    
    def fmp_revenue_product_segmentation(self, symbol: str) -> List[Dict]:
        """Get revenue product segmentation"""
        return self.fmp_request(f"/v4/revenue-product-segmentation", {"symbol": symbol})
    
    def fmp_revenue_geographic_segmentation(self, symbol: str) -> List[Dict]:
        """Get revenue geographic segmentation"""
        return self.fmp_request(f"/v4/revenue-geographic-segmentation", {"symbol": symbol})
    
    def fmp_income_statement_as_reported(self, symbol: str, period: str = "annual", limit: int = 10) -> List[Dict]:
        """Get income statement as reported"""
        return self.fmp_request(f"/v3/income-statement-as-reported/{symbol}", 
                               {"period": period, "limit": limit})
    
    def fmp_balance_sheet_as_reported(self, symbol: str, period: str = "annual", limit: int = 10) -> List[Dict]:
        """Get balance sheet as reported"""
        return self.fmp_request(f"/v3/balance-sheet-statement-as-reported/{symbol}", 
                               {"period": period, "limit": limit})
    
    def fmp_cash_flow_as_reported(self, symbol: str, period: str = "annual", limit: int = 10) -> List[Dict]:
        """Get cash flow as reported"""
        return self.fmp_request(f"/v3/cash-flow-statement-as-reported/{symbol}", 
                               {"period": period, "limit": limit})
    
    def fmp_financial_statement_full_as_reported(self, symbol: str, period: str = "annual") -> Dict:
        """Get full financial statement as reported"""
        return self.fmp_request(f"/v3/financial-statement-full-as-reported/{symbol}", {"period": period})
    
    # === FMP Institutional Ownership ===
    def fmp_institutional_ownership_latest(self, page: int = 0, limit: int = 100) -> List[Dict]:
        """Get latest institutional ownership"""
        return self.fmp_request("/v4/institutional-ownership/list", {"page": page, "limit": limit})
    
    def fmp_institutional_ownership_extract(self, cik: str, year: int, quarter: int) -> List[Dict]:
        """Get institutional ownership extract"""
        return self.fmp_request(f"/v4/institutional-ownership/portfolio-holdings", 
                               {"cik": cik, "date": f"{year}-{quarter:02d}-01"})
    
    def fmp_institutional_ownership_dates(self, cik: str) -> List[str]:
        """Get institutional ownership dates"""
        return self.fmp_request(f"/v4/institutional-ownership/portfolio-date", {"cik": cik})
    
    def fmp_institutional_ownership_holder_analytics(self, symbol: str, year: int, quarter: int, 
                                                   page: int = 0, limit: int = 10) -> List[Dict]:
        """Get institutional holder analytics"""
        return self.fmp_request("/v4/institutional-ownership/institutional-holders/symbol-ownership", 
                               {"symbol": symbol, "date": f"{year}-{quarter:02d}-01", 
                                "page": page, "limit": limit})
    
    def fmp_institutional_ownership_holder_performance(self, cik: str, page: int = 0) -> List[Dict]:
        """Get institutional holder performance"""
        return self.fmp_request("/v4/institutional-ownership/institutional-holders/portfolio-holdings-summary", 
                               {"cik": cik, "page": page})
    
    def fmp_institutional_ownership_holder_industry_breakdown(self, cik: str, year: int, quarter: int) -> List[Dict]:
        """Get institutional holder industry breakdown"""
        return self.fmp_request("/v4/institutional-ownership/industry/portfolio-holdings-by-industry", 
                               {"cik": cik, "date": f"{year}-{quarter:02d}-01"})
    
    def fmp_institutional_ownership_symbol_positions_summary(self, symbol: str, year: int, quarter: int) -> Dict:
        """Get symbol positions summary"""
        return self.fmp_request("/v4/institutional-ownership/symbol-ownership", 
                               {"symbol": symbol, "date": f"{year}-{quarter:02d}-01"})
    
    def fmp_institutional_ownership_industry_summary(self, year: int, quarter: int) -> List[Dict]:
        """Get industry summary"""
        return self.fmp_request("/v4/institutional-ownership/industry/institutional-holdings-by-industry", 
                               {"date": f"{year}-{quarter:02d}-01"})
    
    # === FMP Index Data ===
    def fmp_index_list(self) -> List[Dict]:
        """Get index list"""
        return self.fmp_request("/v3/symbol/available-indexes")
    
    def fmp_batch_index_quotes(self) -> List[Dict]:
        """Get batch index quotes"""
        return self.fmp_request("/v3/quotes/index")
    
    def fmp_sp500_constituent(self) -> List[Dict]:
        """Get S&P 500 constituents"""
        return self.fmp_request("/v3/sp500_constituent")
    
    def fmp_nasdaq_constituent(self) -> List[Dict]:
        """Get NASDAQ constituents"""
        return self.fmp_request("/v3/nasdaq_constituent")
    
    def fmp_dowjones_constituent(self) -> List[Dict]:
        """Get Dow Jones constituents"""
        return self.fmp_request("/v3/dowjones_constituent")
    
    def fmp_historical_sp500_constituent(self) -> List[Dict]:
        """Get historical S&P 500 constituents"""
        return self.fmp_request("/v3/historical/sp500_constituent")
    
    def fmp_historical_nasdaq_constituent(self) -> List[Dict]:
        """Get historical NASDAQ constituents"""
        return self.fmp_request("/v3/historical/nasdaq_constituent")
    
    def fmp_historical_dowjones_constituent(self) -> List[Dict]:
        """Get historical Dow Jones constituents"""
        return self.fmp_request("/v3/historical/dowjones_constituent")
    
    # === FMP Insider Trading Extended ===
    def fmp_insider_trading_latest(self, page: int = 0, limit: int = 100) -> List[Dict]:
        """Get latest insider trading"""
        return self.fmp_request("/v4/insider-trading", {"page": page, "limit": limit})
    
    def fmp_insider_trading_search(self, page: int = 0, limit: int = 100, **filters) -> List[Dict]:
        """Search insider trading with filters"""
        params = {"page": page, "limit": limit}
        params.update(filters)
        return self.fmp_request("/v4/insider-trading-filter", params)
    
    def fmp_insider_trading_reporting_name(self, name: str) -> List[Dict]:
        """Get insider trading by reporting name"""
        return self.fmp_request("/v4/insider-trading", {"reportingName": name})
    
    def fmp_insider_trading_transaction_type(self) -> List[str]:
        """Get insider trading transaction types"""
        return self.fmp_request("/v4/insider-trading-transaction-type")
    
    def fmp_insider_trading_statistics(self, symbol: str) -> Dict:
        """Get insider trading statistics"""
        return self.fmp_request(f"/v4/insider-trading-statistics", {"symbol": symbol})
    
    def fmp_acquisition_of_beneficial_ownership(self, symbol: str) -> List[Dict]:
        """Get acquisition of beneficial ownership"""
        return self.fmp_request("/v4/insider-trading", {"symbol": symbol, "transactionType": "P-Purchase"})
    
    # === FMP Market Performance ===
    def fmp_sector_performance_snapshot(self, date: str = None) -> List[Dict]:
        """Get sector performance snapshot"""
        params = {}
        if date:
            params["date"] = date
        return self.fmp_request("/v3/sectors-performance", params)
    
    def fmp_industry_performance_snapshot(self, date: str = None) -> List[Dict]:
        """Get industry performance snapshot"""
        params = {}
        if date:
            params["date"] = date
        return self.fmp_request("/v3/industries-performance", params)
    
    def fmp_historical_sector_performance(self, sector: str, limit: int = 100) -> List[Dict]:
        """Get historical sector performance"""
        return self.fmp_request(f"/v3/historical-sectors-performance", 
                               {"sector": sector, "limit": limit})
    
    def fmp_historical_industry_performance(self, industry: str, limit: int = 100) -> List[Dict]:
        """Get historical industry performance"""
        return self.fmp_request(f"/v3/historical-industries-performance", 
                               {"industry": industry, "limit": limit})
    
    def fmp_sector_pe_snapshot(self, date: str = None) -> List[Dict]:
        """Get sector P/E snapshot"""
        params = {}
        if date:
            params["date"] = date
        return self.fmp_request("/v4/sector_price_earning_ratio", params)
    
    def fmp_industry_pe_snapshot(self, date: str = None) -> List[Dict]:
        """Get industry P/E snapshot"""
        params = {}
        if date:
            params["date"] = date
        return self.fmp_request("/v4/industry_price_earning_ratio", params)
    
    def fmp_historical_sector_pe(self, sector: str) -> List[Dict]:
        """Get historical sector P/E"""
        return self.fmp_request("/v4/historical-sector-price-earning-ratio", {"sector": sector})
    
    def fmp_historical_industry_pe(self, industry: str) -> List[Dict]:
        """Get historical industry P/E"""
        return self.fmp_request("/v4/historical-industry-price-earning-ratio", {"industry": industry})
    
    # === FMP Market Movers ===
    def fmp_biggest_gainers(self) -> List[Dict]:
        """Get biggest gainers"""
        return self.fmp_request("/v3/stock_market/gainers")
    
    def fmp_biggest_losers(self) -> List[Dict]:
        """Get biggest losers"""
        return self.fmp_request("/v3/stock_market/losers")
    
    def fmp_most_actives(self) -> List[Dict]:
        """Get most active stocks"""
        return self.fmp_request("/v3/stock_market/actives")
    
    # === FMP Market Hours ===
    def fmp_exchange_market_hours(self, exchange: str) -> Dict:
        """Get market hours for exchange"""
        return self.fmp_request(f"/v3/market-hours", {"exchange": exchange})
    
    def fmp_all_exchange_market_hours(self) -> List[Dict]:
        """Get all exchange market hours"""
        return self.fmp_request("/v3/market-hours")
    
    # === FMP News Extended ===
    def fmp_articles(self, page: int = 0, limit: int = 20) -> List[Dict]:
        """Get FMP articles"""
        return self.fmp_request("/v3/fmp/articles", {"page": page, "limit": limit})
    
    def fmp_news_general_latest(self, page: int = 0, limit: int = 20) -> List[Dict]:
        """Get latest general news"""
        return self.fmp_request("/v4/general_news", {"page": page, "limit": limit})
    
    def fmp_news_press_releases_latest(self, page: int = 0, limit: int = 20) -> List[Dict]:
        """Get latest press releases"""
        return self.fmp_request("/v3/press-releases", {"page": page, "limit": limit})
    
    def fmp_news_stock_latest(self, page: int = 0, limit: int = 20) -> List[Dict]:
        """Get latest stock news"""
        return self.fmp_request("/v3/stock_news", {"page": page, "limit": limit})
    
    def fmp_news_crypto_latest(self, page: int = 0, limit: int = 20) -> List[Dict]:
        """Get latest crypto news"""
        return self.fmp_request("/v4/crypto_news", {"page": page, "limit": limit})
    
    def fmp_news_forex_latest(self, page: int = 0, limit: int = 20) -> List[Dict]:
        """Get latest forex news"""
        return self.fmp_request("/v4/forex_news", {"page": page, "limit": limit})
    
    def fmp_news_press_releases(self, symbols: str) -> List[Dict]:
        """Get press releases for symbols"""
        return self.fmp_request("/v3/press-releases", {"symbol": symbols})
    
    def fmp_news_stock(self, symbols: str) -> List[Dict]:
        """Get stock news for symbols"""
        return self.fmp_request("/v3/stock_news", {"tickers": symbols})
    
    def fmp_news_crypto(self, symbols: str) -> List[Dict]:
        """Get crypto news for symbols"""
        return self.fmp_request("/v4/crypto_news", {"symbol": symbols})
    
    def fmp_news_forex(self, symbols: str) -> List[Dict]:
        """Get forex news for symbols"""
        return self.fmp_request("/v4/forex_news", {"symbol": symbols})
    
    # === FMP Technical Indicators Extended ===
    def fmp_sma(self, symbol: str, period_length: int = 10, timeframe: str = "1day") -> List[Dict]:
        """Get Simple Moving Average"""
        return self.fmp_request(f"/v3/technical_indicator/{timeframe}/{symbol}", 
                               {"type": "sma", "period": period_length})
    
    def fmp_ema(self, symbol: str, period_length: int = 10, timeframe: str = "1day") -> List[Dict]:
        """Get Exponential Moving Average"""
        return self.fmp_request(f"/v3/technical_indicator/{timeframe}/{symbol}", 
                               {"type": "ema", "period": period_length})
    
    def fmp_wma(self, symbol: str, period_length: int = 10, timeframe: str = "1day") -> List[Dict]:
        """Get Weighted Moving Average"""
        return self.fmp_request(f"/v3/technical_indicator/{timeframe}/{symbol}", 
                               {"type": "wma", "period": period_length})
    
    def fmp_dema(self, symbol: str, period_length: int = 10, timeframe: str = "1day") -> List[Dict]:
        """Get Double Exponential Moving Average"""
        return self.fmp_request(f"/v3/technical_indicator/{timeframe}/{symbol}", 
                               {"type": "dema", "period": period_length})
    
    def fmp_tema(self, symbol: str, period_length: int = 10, timeframe: str = "1day") -> List[Dict]:
        """Get Triple Exponential Moving Average"""
        return self.fmp_request(f"/v3/technical_indicator/{timeframe}/{symbol}", 
                               {"type": "tema", "period": period_length})
    
    def fmp_rsi(self, symbol: str, period_length: int = 14, timeframe: str = "1day") -> List[Dict]:
        """Get Relative Strength Index"""
        return self.fmp_request(f"/v3/technical_indicator/{timeframe}/{symbol}", 
                               {"type": "rsi", "period": period_length})
    
    def fmp_standard_deviation(self, symbol: str, period_length: int = 10, timeframe: str = "1day") -> List[Dict]:
        """Get Standard Deviation"""
        return self.fmp_request(f"/v3/technical_indicator/{timeframe}/{symbol}", 
                               {"type": "standardDeviation", "period": period_length})
    
    def fmp_williams(self, symbol: str, period_length: int = 14, timeframe: str = "1day") -> List[Dict]:
        """Get Williams %R"""
        return self.fmp_request(f"/v3/technical_indicator/{timeframe}/{symbol}", 
                               {"type": "williams", "period": period_length})
    
    def fmp_adx(self, symbol: str, period_length: int = 14, timeframe: str = "1day") -> List[Dict]:
        """Get Average Directional Index"""
        return self.fmp_request(f"/v3/technical_indicator/{timeframe}/{symbol}", 
                               {"type": "adx", "period": period_length})
    
    # === FMP Quote Extended ===
    def fmp_aftermarket_trade(self, symbol: str) -> Dict:
        """Get aftermarket trade"""
        return self.fmp_request(f"/v4/pre-post-market-trade/{symbol}")
    
    def fmp_aftermarket_quote(self, symbol: str) -> Dict:
        """Get aftermarket quote"""
        return self.fmp_request(f"/v4/pre-post-market/{symbol}")
    
    def fmp_stock_price_change(self, symbol: str) -> Dict:
        """Get stock price change"""
        return self.fmp_request(f"/v3/stock-price-change/{symbol}")
    
    def fmp_batch_quote(self, symbols: List[str]) -> List[Dict]:
        """Get batch quotes"""
        return self.fmp_request("/v3/quote", {"symbols": ",".join(symbols)})
    
    def fmp_batch_quote_short(self, symbols: List[str]) -> List[Dict]:
        """Get batch short quotes"""
        return self.fmp_request("/v3/quote-short", {"symbols": ",".join(symbols)})
    
    def fmp_batch_aftermarket_trade(self, symbols: List[str]) -> List[Dict]:
        """Get batch aftermarket trades"""
        return self.fmp_request("/v4/pre-post-market-trade", {"symbols": ",".join(symbols)})
    
    def fmp_batch_aftermarket_quote(self, symbols: List[str]) -> List[Dict]:
        """Get batch aftermarket quotes"""
        return self.fmp_request("/v4/pre-post-market", {"symbols": ",".join(symbols)})
    
    def fmp_batch_exchange_quote(self, exchange: str) -> List[Dict]:
        """Get batch quotes by exchange"""
        return self.fmp_request(f"/v3/quotes/{exchange.lower()}")
    
    def fmp_batch_mutualfund_quotes(self) -> List[Dict]:
        """Get batch mutual fund quotes"""
        return self.fmp_request("/v3/quotes/mutual_fund")
    
    def fmp_batch_etf_quotes(self) -> List[Dict]:
        """Get batch ETF quotes"""
        return self.fmp_request("/v3/quotes/etf")
    
    # === FMP Search Exchange Variants ===
    def fmp_search_exchange_variants(self, symbol: str) -> List[Dict]:
        """Search exchange variants for symbol"""
        return self.fmp_request("/v3/search", {"query": symbol, "exchange": "all"})
    
    # ==================== ALPACA API ====================
    
    # === Account Management ===
    def alpaca_get_account(self) -> Dict:
        """Get account information"""
        try:
            account = self.trading_client.get_account()
            return account.model_dump()
        except Exception as e:
            return {"error": str(e)}
    
    def alpaca_get_account_configurations(self) -> Dict:
        """Get account configurations"""
        try:
            config = self.trading_client.get_account_configurations()
            return config.model_dump()
        except Exception as e:
            return {"error": str(e)}
    
    def alpaca_update_account_configurations(self, **kwargs) -> Dict:
        """Update account configurations"""
        try:
            config = self.trading_client.set_account_configurations(**kwargs)
            return config.model_dump()
        except Exception as e:
            return {"error": str(e)}
    
    # === Trading - Orders ===
    def alpaca_create_order(self, symbol: str, qty: float = None, notional: float = None,
                           side: str = "buy", order_type: str = "market", 
                           time_in_force: str = "day", **kwargs) -> Dict:
        """Create an order with all possible parameters"""
        try:
            if order_type == "market":
                if qty:
                    request = MarketOrderRequest(
                        symbol=symbol,
                        qty=qty,
                        side=OrderSide(side),
                        time_in_force=TimeInForce(time_in_force),
                        **kwargs
                    )
                else:
                    request = MarketOrderRequest(
                        symbol=symbol,
                        notional=notional,
                        side=OrderSide(side),
                        time_in_force=TimeInForce(time_in_force),
                        **kwargs
                    )
            elif order_type == "limit":
                request = LimitOrderRequest(
                    symbol=symbol,
                    qty=qty,
                    side=OrderSide(side),
                    time_in_force=TimeInForce(time_in_force),
                    limit_price=kwargs.get('limit_price'),
                    **{k: v for k, v in kwargs.items() if k != 'limit_price'}
                )
            elif order_type == "stop":
                request = StopOrderRequest(
                    symbol=symbol,
                    qty=qty,
                    side=OrderSide(side),
                    time_in_force=TimeInForce(time_in_force),
                    stop_price=kwargs.get('stop_price'),
                    **{k: v for k, v in kwargs.items() if k != 'stop_price'}
                )
            elif order_type == "stop_limit":
                request = StopLimitOrderRequest(
                    symbol=symbol,
                    qty=qty,
                    side=OrderSide(side),
                    time_in_force=TimeInForce(time_in_force),
                    stop_price=kwargs.get('stop_price'),
                    limit_price=kwargs.get('limit_price'),
                    **{k: v for k, v in kwargs.items() if k not in ['stop_price', 'limit_price']}
                )
            elif order_type == "trailing_stop":
                request = TrailingStopOrderRequest(
                    symbol=symbol,
                    qty=qty,
                    side=OrderSide(side),
                    time_in_force=TimeInForce(time_in_force),
                    trail_price=kwargs.get('trail_price'),
                    trail_percent=kwargs.get('trail_percent'),
                    **{k: v for k, v in kwargs.items() if k not in ['trail_price', 'trail_percent']}
                )
            
            order = self.trading_client.submit_order(request)
            return order.model_dump()
        except Exception as e:
            return {"error": str(e)}
    
    def alpaca_get_orders(self, status: str = "open", limit: int = 100, **kwargs) -> List[Dict]:
        """Get orders with filters"""
        try:
            request = GetOrdersRequest(
                status=QueryOrderStatus(status),
                limit=limit,
                **kwargs
            )
            orders = self.trading_client.get_orders(request)
            return [order.model_dump() for order in orders]
        except Exception as e:
            return [{"error": str(e)}]
    
    def alpaca_get_order_by_id(self, order_id: str) -> Dict:
        """Get specific order by ID"""
        try:
            order = self.trading_client.get_order_by_id(order_id)
            return order.model_dump()
        except Exception as e:
            return {"error": str(e)}
    
    def alpaca_cancel_order(self, order_id: str) -> Dict:
        """Cancel an order"""
        try:
            self.trading_client.cancel_order_by_id(order_id)
            return {"success": True, "order_id": order_id}
        except Exception as e:
            return {"error": str(e)}
    
    def alpaca_cancel_all_orders(self) -> Dict:
        """Cancel all open orders"""
        try:
            cancelled = self.trading_client.cancel_orders()
            return {"success": True, "cancelled_count": len(cancelled)}
        except Exception as e:
            return {"error": str(e)}
    
    # === Trading - Positions ===
    def alpaca_get_positions(self) -> List[Dict]:
        """Get all positions"""
        try:
            positions = self.trading_client.get_all_positions()
            return [pos.model_dump() for pos in positions]
        except Exception as e:
            return [{"error": str(e)}]
    
    def alpaca_get_position(self, symbol: str) -> Dict:
        """Get specific position"""
        try:
            position = self.trading_client.get_open_position(symbol)
            return position.model_dump()
        except Exception as e:
            return {"error": str(e)}
    
    def alpaca_close_position(self, symbol: str, qty: float = None, percentage: float = None) -> Dict:
        """Close a position"""
        try:
            if percentage:
                request = ClosePositionRequest(percentage=percentage)
            elif qty:
                request = ClosePositionRequest(qty=qty)
            else:
                request = ClosePositionRequest()
            
            order = self.trading_client.close_position(symbol, request)
            return order.model_dump()
        except Exception as e:
            return {"error": str(e)}
    
    def alpaca_close_all_positions(self) -> Dict:
        """Close all positions"""
        try:
            orders = self.trading_client.close_all_positions()
            return {"success": True, "orders": [order.model_dump() for order in orders]}
        except Exception as e:
            return {"error": str(e)}
    
    # === Trading - Assets ===
    def alpaca_get_assets(self, status: str = "active", asset_class: str = None) -> List[Dict]:
        """Get tradable assets"""
        try:
            request = GetAssetsRequest(status=AssetStatus(status))
            if asset_class:
                request.asset_class = AssetClass(asset_class)
            
            assets = self.trading_client.get_all_assets(request)
            return [asset.model_dump() for asset in assets]
        except Exception as e:
            return [{"error": str(e)}]
    
    def alpaca_get_asset(self, symbol: str) -> Dict:
        """Get specific asset"""
        try:
            asset = self.trading_client.get_asset(symbol)
            return asset.model_dump()
        except Exception as e:
            return {"error": str(e)}
    
    # === Trading - Watchlists ===
    def alpaca_get_watchlists(self) -> List[Dict]:
        """Get all watchlists"""
        try:
            watchlists = self.trading_client.get_watchlists()
            return [wl.model_dump() for wl in watchlists]
        except Exception as e:
            return [{"error": str(e)}]
    
    def alpaca_create_watchlist(self, name: str, symbols: List[str]) -> Dict:
        """Create a watchlist"""
        try:
            request = CreateWatchlistRequest(
                name=name,
                symbols=symbols
            )
            watchlist = self.trading_client.create_watchlist(request)
            return watchlist.model_dump()
        except Exception as e:
            return {"error": str(e)}
    
    def alpaca_add_to_watchlist(self, watchlist_id: str, symbol: str) -> Dict:
        """Add symbol to watchlist"""
        try:
            request = UpdateWatchlistRequest(symbols=[symbol])
            watchlist = self.trading_client.add_asset_to_watchlist(watchlist_id, request)
            return watchlist.model_dump()
        except Exception as e:
            return {"error": str(e)}
    
    # === Market Data - Stocks ===
    def alpaca_get_stock_bars(self, symbol: str, timeframe: str = "1Day", 
                             start: str = None, end: str = None, limit: int = 100) -> Dict:
        """Get stock bars"""
        try:
            request = StockBarsRequest(
                symbol_or_symbols=symbol,
                timeframe=TimeFrame[timeframe],
                start=datetime.fromisoformat(start) if start else None,
                end=datetime.fromisoformat(end) if end else None,
                limit=limit
            )
            bars = self.stock_data_client.get_stock_bars(request)
            return {"symbol": symbol, "bars": [bar.model_dump() for bar in bars[symbol]]}
        except Exception as e:
            return {"error": str(e)}
    
    def alpaca_get_stock_quotes(self, symbol: str, start: str = None, end: str = None, limit: int = 100) -> Dict:
        """Get stock quotes"""
        try:
            request = StockQuotesRequest(
                symbol_or_symbols=symbol,
                start=datetime.fromisoformat(start) if start else None,
                end=datetime.fromisoformat(end) if end else None,
                limit=limit
            )
            quotes = self.stock_data_client.get_stock_quotes(request)
            return {"symbol": symbol, "quotes": [quote.model_dump() for quote in quotes[symbol]]}
        except Exception as e:
            return {"error": str(e)}
    
    def alpaca_get_stock_trades(self, symbol: str, start: str = None, end: str = None, limit: int = 100) -> Dict:
        """Get stock trades"""
        try:
            request = StockTradesRequest(
                symbol_or_symbols=symbol,
                start=datetime.fromisoformat(start) if start else None,
                end=datetime.fromisoformat(end) if end else None,
                limit=limit
            )
            trades = self.stock_data_client.get_stock_trades(request)
            return {"symbol": symbol, "trades": [trade.model_dump() for trade in trades[symbol]]}
        except Exception as e:
            return {"error": str(e)}
    
    def alpaca_get_latest_stock_quote(self, symbol: str) -> Dict:
        """Get latest stock quote"""
        try:
            request = StockLatestQuoteRequest(symbol_or_symbols=symbol)
            quote = self.stock_data_client.get_stock_latest_quote(request)
            return {"symbol": symbol, "quote": quote[symbol].model_dump()}
        except Exception as e:
            return {"error": str(e)}
    
    def alpaca_get_latest_stock_bar(self, symbol: str) -> Dict:
        """Get latest stock bar"""
        try:
            request = StockLatestBarRequest(symbol_or_symbols=symbol)
            bar = self.stock_data_client.get_stock_latest_bar(request)
            return {"symbol": symbol, "bar": bar[symbol].model_dump()}
        except Exception as e:
            return {"error": str(e)}
    
    def alpaca_get_stock_snapshot(self, symbol: str) -> Dict:
        """Get stock snapshot"""
        try:
            request = StockSnapshotRequest(symbol_or_symbols=symbol)
            snapshot = self.stock_data_client.get_stock_snapshot(request)
            return {"symbol": symbol, "snapshot": snapshot[symbol].model_dump()}
        except Exception as e:
            return {"error": str(e)}
    
    # === Market Data - Crypto ===
    def alpaca_get_crypto_bars(self, symbol: str, timeframe: str = "1Day",
                              start: str = None, end: str = None, limit: int = 100) -> Dict:
        """Get crypto bars"""
        try:
            request = CryptoBarsRequest(
                symbol_or_symbols=symbol,
                timeframe=TimeFrame[timeframe],
                start=datetime.fromisoformat(start) if start else None,
                end=datetime.fromisoformat(end) if end else None,
                limit=limit
            )
            bars = self.crypto_data_client.get_crypto_bars(request)
            return {"symbol": symbol, "bars": [bar.model_dump() for bar in bars[symbol]]}
        except Exception as e:
            return {"error": str(e)}
    
    def alpaca_get_crypto_quotes(self, symbol: str, start: str = None, end: str = None, limit: int = 100) -> Dict:
        """Get crypto quotes"""
        try:
            request = CryptoQuotesRequest(
                symbol_or_symbols=symbol,
                start=datetime.fromisoformat(start) if start else None,
                end=datetime.fromisoformat(end) if end else None,
                limit=limit
            )
            quotes = self.crypto_data_client.get_crypto_quotes(request)
            return {"symbol": symbol, "quotes": [quote.model_dump() for quote in quotes[symbol]]}
        except Exception as e:
            return {"error": str(e)}
    
    def alpaca_get_crypto_trades(self, symbol: str, start: str = None, end: str = None, limit: int = 100) -> Dict:
        """Get crypto trades"""
        try:
            request = CryptoTradesRequest(
                symbol_or_symbols=symbol,
                start=datetime.fromisoformat(start) if start else None,
                end=datetime.fromisoformat(end) if end else None,
                limit=limit
            )
            trades = self.crypto_data_client.get_crypto_trades(request)
            return {"symbol": symbol, "trades": [trade.model_dump() for trade in trades[symbol]]}
        except Exception as e:
            return {"error": str(e)}
    
    def alpaca_get_latest_crypto_quote(self, symbol: str) -> Dict:
        """Get latest crypto quote"""
        try:
            request = CryptoLatestQuoteRequest(symbol_or_symbols=symbol)
            quote = self.crypto_data_client.get_crypto_latest_quote(request)
            return {"symbol": symbol, "quote": quote[symbol].model_dump()}
        except Exception as e:
            return {"error": str(e)}
    
    def alpaca_get_latest_crypto_bar(self, symbol: str) -> Dict:
        """Get latest crypto bar"""
        try:
            request = CryptoLatestBarRequest(symbol_or_symbols=symbol)
            bar = self.crypto_data_client.get_crypto_latest_bar(request)
            return {"symbol": symbol, "bar": bar[symbol].model_dump()}
        except Exception as e:
            return {"error": str(e)}
    
    def alpaca_get_crypto_snapshot(self, symbol: str) -> Dict:
        """Get crypto snapshot"""
        try:
            request = CryptoSnapshotRequest(symbol_or_symbols=symbol)
            snapshot = self.crypto_data_client.get_crypto_snapshot(request)
            return {"symbol": symbol, "snapshot": snapshot[symbol].model_dump()}
        except Exception as e:
            return {"error": str(e)}
    
    # === Market Data - News ===
    def alpaca_get_news(self, symbols: List[str] = None, start: str = None, 
                       end: str = None, limit: int = 50) -> List[Dict]:
        """Get news articles"""
        try:
            request = NewsRequest(
                symbols=symbols,
                start=datetime.fromisoformat(start) if start else None,
                end=datetime.fromisoformat(end) if end else None,
                limit=limit
            )
            news = self.stock_data_client.get_news(request)
            return [article.model_dump() for article in news]
        except Exception as e:
            return [{"error": str(e)}]
    
    # === Options Trading (if enabled) ===
    def alpaca_get_option_contracts(self, underlying_symbol: str, 
                                   expiration_date: str = None,
                                   strike_price: float = None,
                                   option_type: str = None) -> List[Dict]:
        """Get option contracts"""
        try:
            # Note: Options trading requires additional permissions
            contracts = self.trading_client.get_option_contracts(
                underlying_symbols=underlying_symbol,
                expiration_date=expiration_date,
                strike_price=strike_price,
                type=option_type
            )
            return [contract.model_dump() for contract in contracts]
        except Exception as e:
            return [{"error": str(e)}]
    
    # === Portfolio History ===
    def alpaca_get_portfolio_history(self, period: str = "1M", timeframe: str = "1D") -> Dict:
        """Get portfolio history"""
        try:
            history = self.trading_client.get_portfolio_history(
                period=period,
                timeframe=timeframe
            )
            return history.model_dump()
        except Exception as e:
            return {"error": str(e)}
    
    # === Clock & Calendar ===
    def alpaca_get_clock(self) -> Dict:
        """Get market clock"""
        try:
            clock = self.trading_client.get_clock()
            return clock.model_dump()
        except Exception as e:
            return {"error": str(e)}
    
    def alpaca_get_calendar(self, start: str = None, end: str = None) -> List[Dict]:
        """Get market calendar"""
        try:
            calendar = self.trading_client.get_calendar(start=start, end=end)
            return [day.model_dump() for day in calendar]
        except Exception as e:
            return [{"error": str(e)}]
    
    # === Announcements ===
    def alpaca_get_corporate_actions(self, symbols: List[str] = None, 
                                    action_types: List[str] = None) -> List[Dict]:
        """Get corporate action announcements"""
        try:
            request = GetCorporateAnnouncementsRequest(
                ca_types=action_types,
                symbols=symbols
            )
            actions = self.trading_client.get_corporate_announcements(request)
            return [action.model_dump() for action in actions]
        except Exception as e:
            return [{"error": str(e)}]
    
    # === Account Activities ===
    def alpaca_get_account_activities(self, activity_type: str = None) -> List[Dict]:
        """Get account activities"""
        try:
            if activity_type:
                activities = self.trading_client.get_activities(activity_type)
            else:
                activities = self.trading_client.get_activities()
            return [activity.model_dump() for activity in activities]
        except Exception as e:
            return [{"error": str(e)}]
    
    # === Crypto Funding ===
    def alpaca_get_crypto_wallets(self) -> List[Dict]:
        """Get crypto funding wallets"""
        try:
            response = self.alpaca_rest.get('/v2/crypto/funding/wallets')
            return response
        except Exception as e:
            return [{"error": str(e)}]
    
    def alpaca_get_crypto_transfers(self) -> List[Dict]:
        """Get crypto funding transfers"""
        try:
            response = self.alpaca_rest.get('/v2/crypto/funding/transfers')
            return response
        except Exception as e:
            return [{"error": str(e)}]
    
    def alpaca_create_crypto_withdrawal(self, address: str, amount: float, asset: str) -> Dict:
        """Create crypto withdrawal"""
        try:
            data = {
                "address": address,
                "amount": amount,
                "asset": asset
            }
            response = self.alpaca_rest.post('/v2/crypto/funding/withdrawals', json=data)
            return response
        except Exception as e:
            return {"error": str(e)}
    
    def alpaca_get_crypto_transfer_by_id(self, transfer_id: str) -> Dict:
        """Get specific crypto transfer"""
        try:
            response = self.alpaca_rest.get(f'/v2/crypto/funding/transfers/{transfer_id}')
            return response
        except Exception as e:
            return {"error": str(e)}
    
    # === Whitelists ===
    def alpaca_get_whitelists(self) -> List[Dict]:
        """Get wallet whitelists"""
        try:
            response = self.alpaca_rest.get('/v2/wallets/whitelists')
            return response
        except Exception as e:
            return [{"error": str(e)}]
    
    def alpaca_create_whitelist(self, address: str, asset: str, name: str = None) -> Dict:
        """Create wallet whitelist"""
        try:
            data = {
                "address": address,
                "asset": asset
            }
            if name:
                data["name"] = name
            response = self.alpaca_rest.post('/v2/wallets/whitelists', json=data)
            return response
        except Exception as e:
            return {"error": str(e)}
    
    def alpaca_delete_whitelist(self, address: str) -> Dict:
        """Delete wallet whitelist"""
        try:
            response = self.alpaca_rest.delete(f'/v2/wallets/whitelists/{address}')
            return {"success": True, "address": address}
        except Exception as e:
            return {"error": str(e)}
    
    def alpaca_get_gas_fees(self) -> Dict:
        """Get current gas fees"""
        try:
            response = self.alpaca_rest.get('/v2/wallets/gas_fees')
            return response
        except Exception as e:
            return {"error": str(e)}
    
    # === Options Market Data ===
    def alpaca_get_option_bars(self, symbols: List[str], timeframe: str = "1Day",
                              start: str = None, end: str = None, limit: int = 100) -> Dict:
        """Get option bars"""
        try:
            from alpaca.data.requests import OptionBarsRequest
            request = OptionBarsRequest(
                symbol_or_symbols=symbols,
                timeframe=TimeFrame[timeframe],
                start=datetime.fromisoformat(start) if start else None,
                end=datetime.fromisoformat(end) if end else None,
                limit=limit
            )
            # Note: Requires options data subscription
            bars = self.stock_data_client.get_option_bars(request)
            return {"symbols": symbols, "bars": {sym: [bar.model_dump() for bar in bars[sym]] for sym in symbols}}
        except Exception as e:
            return {"error": str(e)}
    
    def alpaca_get_option_latest_quotes(self, symbols: List[str]) -> Dict:
        """Get latest option quotes"""
        try:
            from alpaca.data.requests import OptionLatestQuoteRequest
            request = OptionLatestQuoteRequest(symbol_or_symbols=symbols)
            quotes = self.stock_data_client.get_option_latest_quote(request)
            return {"symbols": symbols, "quotes": {sym: quote.model_dump() for sym, quote in quotes.items()}}
        except Exception as e:
            return {"error": str(e)}
    
    def alpaca_get_option_snapshots(self, symbols: List[str]) -> Dict:
        """Get option snapshots"""
        try:
            from alpaca.data.requests import OptionSnapshotRequest
            request = OptionSnapshotRequest(symbol_or_symbols=symbols)
            snapshots = self.stock_data_client.get_option_snapshot(request)
            return {"symbols": symbols, "snapshots": {sym: snap.model_dump() for sym, snap in snapshots.items()}}
        except Exception as e:
            return {"error": str(e)}
    
    def alpaca_get_option_chains(self, underlying_symbol: str, expiration_date: str = None) -> Dict:
        """Get option chains for underlying symbol"""
        try:
            from alpaca.data.requests import OptionChainRequest
            request = OptionChainRequest(
                underlying_symbol=underlying_symbol,
                expiration_date=expiration_date
            )
            chains = self.stock_data_client.get_option_chain(request)
            return {"underlying": underlying_symbol, "chains": chains.model_dump()}
        except Exception as e:
            return {"error": str(e)}
    
    # === Stock Market Data - Additional ===
    def alpaca_get_stock_auctions(self, symbols: List[str], start: str = None, 
                                 end: str = None, limit: int = 100) -> Dict:
        """Get stock auction data"""
        try:
            from alpaca.data.requests import StockAuctionsRequest
            request = StockAuctionsRequest(
                symbol_or_symbols=symbols,
                start=datetime.fromisoformat(start) if start else None,
                end=datetime.fromisoformat(end) if end else None,
                limit=limit
            )
            auctions = self.stock_data_client.get_stock_auctions(request)
            return {"symbols": symbols, "auctions": {sym: [auc.model_dump() for auc in auctions[sym]] for sym in symbols}}
        except Exception as e:
            return {"error": str(e)}
    
    def alpaca_get_stock_condition_codes(self) -> Dict:
        """Get stock condition codes"""
        try:
            codes = self.stock_data_client.get_stock_condition_codes()
            return {"condition_codes": codes}
        except Exception as e:
            return {"error": str(e)}
    
    def alpaca_get_stock_exchanges(self) -> Dict:
        """Get stock exchanges"""
        try:
            exchanges = self.stock_data_client.get_stock_exchanges()
            return {"exchanges": exchanges}
        except Exception as e:
            return {"error": str(e)}
    
    # === Crypto Market Data - Additional ===
    def alpaca_get_crypto_orderbook_latest(self, symbols: List[str]) -> Dict:
        """Get latest crypto orderbook"""
        try:
            from alpaca.data.requests import CryptoLatestOrderbookRequest
            request = CryptoLatestOrderbookRequest(symbol_or_symbols=symbols)
            orderbooks = self.crypto_data_client.get_crypto_latest_orderbook(request)
            return {"symbols": symbols, "orderbooks": {sym: ob.model_dump() for sym, ob in orderbooks.items()}}
        except Exception as e:
            return {"error": str(e)}
    
    # === Screener ===
    def alpaca_get_most_active_stocks(self, top: int = 10) -> List[Dict]:
        """Get most active stocks"""
        try:
            from alpaca.data.requests import StockMostActivesRequest
            request = StockMostActivesRequest(top=top)
            stocks = self.stock_data_client.get_stock_most_actives(request)
            return [stock.model_dump() for stock in stocks]
        except Exception as e:
            return [{"error": str(e)}]
    
    # === Logos ===
    def alpaca_get_logo(self, symbol: str) -> Dict:
        """Get company logo"""
        try:
            response = self.alpaca_rest.get(f'/v2/logos/{symbol}')
            return {"symbol": symbol, "logo_url": response}
        except Exception as e:
            return {"error": str(e)}
    
    # === US Treasuries ===
    def alpaca_get_us_treasuries(self) -> List[Dict]:
        """Get US Treasury securities"""
        try:
            treasuries = self.trading_client.get_treasuries()
            return [treasury.model_dump() for treasury in treasuries]
        except Exception as e:
            return [{"error": str(e)}]
    
    # === Options Trading - Additional ===
    def alpaca_exercise_option(self, symbol: str) -> Dict:
        """Exercise an option position"""
        try:
            order = self.trading_client.exercise_option_position(symbol)
            return order.model_dump()
        except Exception as e:
            return {"error": str(e)}
    
    # === Watchlist Management - Additional ===
    def alpaca_get_watchlist_by_id(self, watchlist_id: str) -> Dict:
        """Get watchlist by ID"""
        try:
            watchlist = self.trading_client.get_watchlist(watchlist_id)
            return watchlist.model_dump()
        except Exception as e:
            return {"error": str(e)}
    
    def alpaca_update_watchlist(self, watchlist_id: str, name: str = None, symbols: List[str] = None) -> Dict:
        """Update watchlist"""
        try:
            request = UpdateWatchlistRequest()
            if name:
                request.name = name
            if symbols:
                request.symbols = symbols
            watchlist = self.trading_client.update_watchlist(watchlist_id, request)
            return watchlist.model_dump()
        except Exception as e:
            return {"error": str(e)}
    
    def alpaca_delete_watchlist(self, watchlist_id: str) -> Dict:
        """Delete watchlist"""
        try:
            self.trading_client.delete_watchlist(watchlist_id)
            return {"success": True, "watchlist_id": watchlist_id}
        except Exception as e:
            return {"error": str(e)}
    
    def alpaca_remove_from_watchlist(self, watchlist_id: str, symbol: str) -> Dict:
        """Remove symbol from watchlist"""
        try:
            self.trading_client.remove_asset_from_watchlist(watchlist_id, symbol)
            return {"success": True, "watchlist_id": watchlist_id, "symbol": symbol}
        except Exception as e:
            return {"error": str(e)}
    
    # === Order Management - Additional ===
    def alpaca_get_order_by_client_id(self, client_order_id: str) -> Dict:
        """Get order by client order ID"""
        try:
            order = self.trading_client.get_order_by_client_id(client_order_id)
            return order.model_dump()
        except Exception as e:
            return {"error": str(e)}
    
    def alpaca_replace_order(self, order_id: str, qty: float = None, limit_price: float = None,
                           stop_price: float = None, trail: float = None, time_in_force: str = None) -> Dict:
        """Replace an existing order"""
        try:
            from alpaca.trading.requests import ReplaceOrderRequest
            request = ReplaceOrderRequest()
            if qty:
                request.qty = qty
            if limit_price:
                request.limit_price = limit_price
            if stop_price:
                request.stop_price = stop_price
            if trail:
                request.trail = trail
            if time_in_force:
                request.time_in_force = TimeInForce(time_in_force)
            
            order = self.trading_client.replace_order_by_id(order_id, request)
            return order.model_dump()
        except Exception as e:
            return {"error": str(e)}
    
    # ==================== HELPER METHODS ====================
    
    def get_news(self, search: str = None, tickers: str = None, limit: int = 50) -> List[Dict]:
        """Get news articles - wrapper for FMP stock news"""
        if search:
            # Search for news by keyword
            all_news = self.fmp_stock_news(limit=limit)
            # Check if we got an error
            if isinstance(all_news, dict) and 'error' in all_news:
                logger.warning(f"FMP news error: {all_news.get('error')}")
                return []
            elif isinstance(all_news, list):
                # Filter news by search term
                filtered_news = []
                for article in all_news:
                    if search.lower() in article.get('title', '').lower() or \
                       search.lower() in article.get('text', '').lower():
                        filtered_news.append(article)
                return filtered_news[:limit]
            else:
                return []
        elif tickers:
            result = self.fmp_stock_news(symbol=tickers, limit=limit)
            if isinstance(result, dict) and 'error' in result:
                logger.warning(f"FMP news error: {result.get('error')}")
                return []
            elif isinstance(result, list):
                return result
            else:
                return []
        else:
            result = self.fmp_stock_news(limit=limit)
            if isinstance(result, dict) and 'error' in result:
                logger.warning(f"FMP news error: {result.get('error')}")
                return []
            elif isinstance(result, list):
                return result
            else:
                return []
    
    def get_insider_trades(self, symbol: str = None, limit: int = 50) -> List[Dict]:
        """Get insider trading data - wrapper for FMP insider trading"""
        result = self.fmp_insider_trading(symbol=symbol, page=0)
        # Ensure we always return a list
        if isinstance(result, dict) and 'error' in result:
            logger.warning(f"FMP insider trading error: {result.get('error')}")
            return []  # Return empty list on error
        elif isinstance(result, list):
            return result[:limit]
        else:
            return []
    
    def get_senate_trades(self, symbol: str = None, limit: int = 50) -> List[Dict]:
        """Get Senate trading data - wrapper for FMP Senate trading"""
        trades = self.fmp_senate_trading(symbol=symbol)
        # Ensure we always return a list
        if isinstance(trades, dict) and 'error' in trades:
            logger.warning(f"FMP Senate trading error: {trades.get('error')}")
            return []
        elif isinstance(trades, list):
            return trades[:limit]
        else:
            return []
    
    def get_house_trades(self, symbol: str = None, limit: int = 50) -> List[Dict]:
        """Get House trading data - wrapper for FMP House trading"""
        trades = self.fmp_house_trading(symbol=symbol)
        # Ensure we always return a list
        if isinstance(trades, dict) and 'error' in trades:
            logger.warning(f"FMP House trading error: {trades.get('error')}")
            return []
        elif isinstance(trades, list):
            return trades[:limit]
        else:
            return []
    
    # ==================== COMPREHENSIVE ANALYSIS ====================
    
    def get_market_status(self) -> Dict:
        """Get comprehensive market status"""
        try:
            clock = self.alpaca_get_clock()
            return {
                "is_open": clock.get("is_open", False),
                "next_open": clock.get("next_open"),
                "next_close": clock.get("next_close"),
                "timestamp": clock.get("timestamp")
            }
        except:
            return {"is_open": False, "error": "Unable to get market status"}
    
    def search_all_sources(self, query: str) -> Dict:
        """Search across all available sources"""
        results = {
            "fmp_search": self.fmp_search_symbol(query),
            "alpaca_assets": [],
            "fmp_company_search": self.fmp_search_name(query)
        }
        
        # Search Alpaca assets
        try:
            assets = self.alpaca_get_assets()
            results["alpaca_assets"] = [
                asset for asset in assets 
                if query.upper() in asset.get("symbol", "") or 
                   query.lower() in asset.get("name", "").lower()
            ][:10]  # Limit to 10 results
        except:
            pass
        
        return results
    
    def get_comprehensive_analysis(self, symbol: str) -> Dict:
        """Get comprehensive analysis from all sources"""
        analysis = {
            "symbol": symbol,
            "timestamp": datetime.now().isoformat(),
            "fmp_data": {},
            "alpaca_data": {},
            "technical_indicators": {},
            "fundamental_data": {},
            "news_sentiment": {},
            "insider_activity": {}
        }
        
        # FMP Data
        analysis["fmp_data"]["profile"] = self.fmp_company_profile(symbol)
        analysis["fmp_data"]["quote"] = self.fmp_quote(symbol)
        analysis["fmp_data"]["rating"] = self.fmp_rating(symbol)
        analysis["fmp_data"]["price_target"] = self.fmp_price_target_summary(symbol)
        analysis["fmp_data"]["dcf"] = self.fmp_dcf(symbol)
        
        # Alpaca Data
        analysis["alpaca_data"]["asset"] = self.alpaca_get_asset(symbol)
        analysis["alpaca_data"]["latest_quote"] = self.alpaca_get_latest_stock_quote(symbol)
        analysis["alpaca_data"]["snapshot"] = self.alpaca_get_stock_snapshot(symbol)
        
        # Technical Indicators from FMP
        for indicator in ["SMA", "EMA", "RSI", "MACD"]:
            analysis["technical_indicators"][indicator] = self.fmp_technical_indicator(symbol, indicator)
        
        # Fundamental Data
        analysis["fundamental_data"]["income_statement"] = self.fmp_income_statement(symbol, limit=1)
        analysis["fundamental_data"]["balance_sheet"] = self.fmp_balance_sheet(symbol, limit=1)
        analysis["fundamental_data"]["cash_flow"] = self.fmp_cash_flow(symbol, limit=1)
        analysis["fundamental_data"]["ratios"] = self.fmp_ratios(symbol, limit=1)
        analysis["fundamental_data"]["key_metrics"] = self.fmp_key_metrics(symbol, limit=1)
        
        # News and Sentiment
        analysis["news_sentiment"]["fmp_news"] = self.fmp_stock_news(symbol, limit=10)
        analysis["news_sentiment"]["alpaca_news"] = self.alpaca_get_news([symbol], limit=10)
        
        # Insider Activity
        analysis["insider_activity"]["insider_trades"] = self.fmp_insider_trading(symbol)
        analysis["insider_activity"]["senate_trades"] = self.fmp_senate_trading(symbol)
        analysis["insider_activity"]["house_trades"] = self.fmp_house_trading(symbol)
        
        return analysis 