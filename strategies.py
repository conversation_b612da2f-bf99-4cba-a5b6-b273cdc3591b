"""
ATLAS Comprehensive Trading Strategies Library
Multi-asset strategy implementations across stocks, options, crypto, and fundamentals
"""

import requests
import os
import numpy as np
import pandas as pd
import time
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
# Performance monitoring removed - functionality integrated inline
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import logging
import pytz
import math

# Import symbol formatter
try:
    from utils.symbol_formatter import format_symbol, detect_asset_type
except ImportError:
    # Fallback if utils not available
    def format_symbol(symbol: str, asset_type: str = None) -> str:
        return symbol.upper()
    def detect_asset_type(symbol: str) -> str:
        """Detect if symbol is stock, crypto, or other asset type."""
        # Common crypto symbols
        crypto_symbols = {
            'BTC/USD', 'ETH/USD', 'LTC/USD', 'BCH/USD', 'XRP/USD', 'ADA/USD', 
            'DOT/USD', 'LINK/USD', 'UNI/USD', 'AAVE/USD', 'SUSHI/USD', 'ALGO/USD',
            'BTCUSD', 'ETHUSD', 'LTCUSD', 'BCHUSD', 'XRPUSD', 'ADAUSD',
            'DOTUSD', 'LINKUSD', 'UNIUSD', 'AAVEUSD', 'SUSHIUSD', 'ALGOUSD'
        }
        
        # Check if it's a crypto symbol
        if symbol.upper() in crypto_symbols:
            return 'crypto'
        
        # Check for crypto patterns
        if '/' in symbol and 'USD' in symbol.upper():
            return 'crypto'
        
        if symbol.upper().endswith('USD') and len(symbol) > 4:
            return 'crypto'
        
        # Default to stock
        return 'stock'

# Import S&P 500 symbols
from sp500_symbols import get_sp500_symbols

logger = logging.getLogger(__name__)


# =============================================================================
# MARKET HOURS UTILITIES
# =============================================================================

def is_stock_market_open():
    """
    Returns True if right now is between 9:30 AM and 4:00 PM US/Eastern on a weekday.
    Otherwise returns False.
    """
    # Get current time in New York
    now_et = datetime.now(pytz.timezone("US/Eastern"))
    
    # If it's Saturday (5) or Sunday (6), the market is closed
    if now_et.weekday() >= 5:
        return False
    
    # Build today's 9:30 AM and 4:00 PM timestamps
    open_dt = now_et.replace(hour=9, minute=30, second=0, microsecond=0)
    close_dt = now_et.replace(hour=16, minute=0, second=0, microsecond=0)
    
    # Return True only if current time is within that window
    return open_dt <= now_et <= close_dt


def is_crypto_market_open():
    """Crypto markets are always open"""
    return True


def is_extended_hours():
    """Check if we're in pre-market (4:00 AM - 9:30 AM) or after-hours (4:00 PM - 8:00 PM)"""
    now_et = datetime.now(pytz.timezone("US/Eastern"))
    
    if now_et.weekday() >= 5:
        return False
        
    hour = now_et.hour
    minute = now_et.minute
    
    # Pre-market: 4:00 AM - 9:30 AM
    if (hour == 4 and minute >= 0) or (hour > 4 and hour < 9) or (hour == 9 and minute < 30):
        return True
    
    # After-hours: 4:00 PM - 8:00 PM
    if (hour == 16 and minute >= 0) or (hour > 16 and hour < 20):
        return True
        
    return False


# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

def compute_rsi(prices: pd.Series, period: int = 14) -> pd.Series:
    """Calculate RSI indicator."""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))


def compute_bollinger_bands(prices: pd.Series, period: int = 20, std_dev: float = 2.0) -> Tuple[pd.Series, pd.Series, pd.Series]:
    """Calculate Bollinger Bands."""
    sma = prices.rolling(period).mean()
    std = prices.rolling(period).std()
    upper = sma + (std * std_dev)
    lower = sma - (std * std_dev)
    return upper, sma, lower


def compute_ema(prices: pd.Series, period: int) -> pd.Series:
    """Calculate Exponential Moving Average."""
    return prices.ewm(span=period, adjust=False).mean()


def compute_macd(prices: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
    """Calculate MACD indicator."""
    ema_fast = compute_ema(prices, fast)
    ema_slow = compute_ema(prices, slow)
    macd_line = ema_fast - ema_slow
    signal_line = compute_ema(macd_line, signal)
    histogram = macd_line - signal_line
    return macd_line, signal_line, histogram


def get_market_data(symbol: str, timeframe: str = "1Day", limit: int = 100) -> pd.DataFrame:
    """Get market data for analysis from Alpaca API."""
    try:
        # Detect asset type and format symbol properly
        asset_type = detect_asset_type(symbol)
        formatted_symbol = format_symbol(symbol, asset_type)
        is_crypto = (asset_type == 'crypto')
        
        # For stocks, check market hours
        if not is_crypto and not is_stock_market_open() and not is_extended_hours():
            logger.info(f"Stock market closed for {formatted_symbol}, using last available data")
            # Use a longer lookback period to get the most recent trading day
            limit = limit * 2
        
        # Get Alpaca credentials from environment variables
        alpaca_key = os.getenv("APCA_API_KEY_ID")
        alpaca_secret = os.getenv("APCA_API_SECRET_KEY")

        if not alpaca_key or not alpaca_secret:
            logger.warning(f"Missing Alpaca credentials for {symbol}, using simulated data")
            return get_simulated_data(symbol, limit)

        # Use paper trading data endpoint for paper accounts
        alpaca_base = "https://paper-api.alpaca.markets"
        headers = {
            "APCA-API-KEY-ID": alpaca_key,
            "APCA-API-SECRET-KEY": alpaca_secret
        }
        
        # Calculate start date for historical data
        end_date = datetime.now()
        
        # For intraday timeframes during closed market, use last trading day
        if timeframe in ["1Min", "5Min", "15Min", "30Min", "1Hour"] and not is_crypto:
            if not is_stock_market_open():
                # Go back to last trading day
                days_back = 1 if end_date.weekday() < 5 else 3
                end_date = end_date - timedelta(days=days_back)
        
        start_date = end_date - timedelta(days=limit * 2)  # Extra buffer
        
        # Map timeframes
        timeframe_map = {
            "1Min": "1Min",
            "5Min": "5Min", 
            "15Min": "15Min",
            "30Min": "30Min",
            "1Hour": "1Hour",
            "1Day": "1Day"
        }
        
        alpaca_timeframe = timeframe_map.get(timeframe, "1Day")
        
        # Use paper trading API endpoints for both stocks and crypto
        if is_crypto:
            endpoint = f"{alpaca_base}/v2/crypto/{formatted_symbol}/bars"
            params = {
                "timeframe": alpaca_timeframe,
                "start": start_date.strftime("%Y-%m-%d"),
                "end": end_date.strftime("%Y-%m-%d"),
                "limit": limit
            }
        else:
            endpoint = f"{alpaca_base}/v2/stocks/{formatted_symbol}/bars"
            params = {
                "timeframe": alpaca_timeframe,
                "start": start_date.strftime("%Y-%m-%d"),
                "end": end_date.strftime("%Y-%m-%d"),
                "limit": limit
            }
        
        response = requests.get(
            endpoint,
            headers=headers,
            params=params,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            bars = data.get("bars", [])

            if bars:
                # Convert to DataFrame and standardize to lowercase column names
                df = pd.DataFrame(bars)
                df['timestamp'] = pd.to_datetime(df['t'])
                df = df.rename(columns={
                    'o': 'open',
                    'h': 'high',
                    'l': 'low',
                    'c': 'close',
                    'v': 'volume'
                })
                df = df.sort_values('timestamp').reset_index(drop=True)

                # Always return the DataFrame, even with less data
                # The scanners will decide if they have enough data
                if len(df) < 20:
                    logger.warning(f"Insufficient data for {symbol}: only {len(df)} bars")
                else:
                    logger.info(f"Got {len(df)} bars for {symbol}")
                return df
        else:
            logger.error(f"Alpaca API error for {symbol}: {response.status_code}")
            if response.status_code == 403:
                logger.error(f"403 Forbidden - Check API permissions for {symbol}")

        # Fallback to simulated data if API fails
        logger.info(f"Using simulated data for {symbol}")
        return get_simulated_data(symbol, limit)
        
    except Exception as e:
        logger.error(f"Error fetching data for {symbol}: {str(e)}")
        # Fallback to simulated data on any error
        return get_simulated_data(symbol, limit)


def get_simulated_data(symbol: str, limit: int) -> pd.DataFrame:
    """Generate simulated market data for testing."""
    dates = pd.date_range(start='2024-01-01', periods=limit, freq='D')
    base_price = 100
    
    # Generate realistic price data
    returns = np.random.normal(0.001, 0.02, limit)
    prices = [base_price]
    for r in returns[1:]:
        prices.append(prices[-1] * (1 + r))
    
    return pd.DataFrame({
        'timestamp': dates,
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
        'close': prices,
        'volume': [int(np.random.randint(100000, 1000000)) for _ in prices]
    })


def batch_scan_wrapper(scan_func, symbols: List[str], batch_size: int = 50, **kwargs) -> List[Dict]:
    """
    Wrapper to process large symbol lists in batches with parallel processing.
    """
    if len(symbols) <= batch_size:
        # Small list, process normally
        return scan_func(symbols, **kwargs)
    
    all_signals = []
    
    # Split into batches
    batches = [symbols[i:i + batch_size] for i in range(0, len(symbols), batch_size)]
    
    # Process batches in parallel
    with ThreadPoolExecutor(max_workers=5) as executor:
        futures = [executor.submit(scan_func, batch, **kwargs) for batch in batches]
        
        for future in as_completed(futures):
            try:
                batch_signals = future.result(timeout=20)
                all_signals.extend(batch_signals)
            except Exception as e:
                logger.error(f"Batch processing error: {str(e)}")
    
    return all_signals


# =============================================================================
# STOCKS & ETFs STRATEGIES
# =============================================================================

def scan_ma_crossover(symbols: List[str], short: int = 8, long: int = 21, limit: int = None) -> List[Dict]:
    """Scan for EMA crossover signals."""
    signals = []
    
    for symbol in symbols:
        try:
            df = get_market_data(symbol)

            if df is None or len(df) < long:
                continue
                
            # Use standardized lowercase column names
            close_col = 'close'
            open_col = 'open'
            high_col = 'high'
            low_col = 'low'
            volume_col = 'volume'
            
            df['ema_short'] = compute_ema(df[close_col], short)
            df['ema_long'] = compute_ema(df[close_col], long)
            
            # Check for crossover
            cross_up = (df['ema_short'].iloc[-2] <= df['ema_long'].iloc[-2] and 
                       df['ema_short'].iloc[-1] > df['ema_long'].iloc[-1])
            
            if cross_up:
                signals.append({
                    "symbol": symbol,
                    "algorithm": f"EMA Crossover ({short}/{long})",
                    "signal": "BUY",
                    "entry_price": df[close_col].iloc[-1],
                    "confidence": "High",
                    "timeframe": "1D",
                    "setup_details": {
                        "short_ema": round(df['ema_short'].iloc[-1], 2),
                        "long_ema": round(df['ema_long'].iloc[-1], 2),
                        "crossover_confirmed": True,
                        "volume_average": int(df[volume_col].rolling(20).mean().iloc[-1])
                    }
                })
                
                # Apply limit if specified
                if limit and len(signals) >= limit:
                    break
        except Exception as e:
            logger.error(f"Error scanning MA crossover for {symbol}: {str(e)}")
            continue
    
    return signals[:limit] if limit else signals


def scan_bollinger_reversion(symbols: List[str], period: int = 20, std_dev: float = 2.0) -> List[Dict]:
    """Scan for Bollinger Band mean reversion setups."""
    signals = []
    
    for symbol in symbols:
        try:
            df = get_market_data(symbol)

            if df is None or len(df) < period:
                continue
                
            # Use standardized lowercase column names
            close_col = 'close'
            open_col = 'open'
            high_col = 'high'
            low_col = 'low'
            volume_col = 'volume'

            upper, middle, lower = compute_bollinger_bands(df[close_col], period, std_dev)
            rsi = compute_rsi(df[close_col])
        
            # Long signal: price touches lower band with oversold RSI
            long_signal = (df[close_col].iloc[-1] <= lower.iloc[-1] and rsi.iloc[-1] < 30)
            
            # Short signal: price touches upper band with overbought RSI  
            short_signal = (df[close_col].iloc[-1] >= upper.iloc[-1] and rsi.iloc[-1] > 70)
            
            if long_signal or short_signal:
                signals.append({
                    "symbol": symbol,
                    "algorithm": f"Bollinger Mean Reversion ({period}, {std_dev})",
                    "signal": "BUY" if long_signal else "SELL",
                    "entry_price": df[close_col].iloc[-1],
                    "confidence": "Medium",
                    "timeframe": "1D",
                    "setup_details": {
                        "bb_upper": round(upper.iloc[-1], 2),
                        "bb_middle": round(middle.iloc[-1], 2),
                        "bb_lower": round(lower.iloc[-1], 2),
                        "rsi": round(rsi.iloc[-1], 2),
                        "mean_reversion_trigger": "BB touch + RSI confirmation"
                    }
                })
        except Exception as e:
            logger.error(f"Error scanning Bollinger reversion for {symbol}: {str(e)}")
            continue
    
    return signals


def scan_donchian_breakout(symbols: List[str], period: int = 20) -> List[Dict]:
    """Scan for Donchian Channel breakouts."""
    signals = []
    
    for symbol in symbols:
        try:
            df = get_market_data(symbol)

            if df is None or len(df) < period:
                continue
                
            # Use standardized lowercase column names
            close_col = 'close'
            open_col = 'open'
            high_col = 'high'
            low_col = 'low'
            volume_col = 'volume'

            highest_high = df[high_col].rolling(period).max()
            lowest_low = df[low_col].rolling(period).min()
            
            # Breakout signals
            long_breakout = df[close_col].iloc[-1] > highest_high.iloc[-2]
            short_breakout = df[close_col].iloc[-1] < lowest_low.iloc[-2]
            
            if long_breakout or short_breakout:
                signals.append({
                    "symbol": symbol,
                    "algorithm": f"Donchian Breakout ({period})",
                    "signal": "BUY" if long_breakout else "SELL",
                    "entry_price": df[close_col].iloc[-1],
                    "confidence": "High",
                    "timeframe": "1D",
                    "setup_details": {
                        "channel_high": round(highest_high.iloc[-2], 2),
                        "channel_low": round(lowest_low.iloc[-2], 2),
                        "breakout_level": round(highest_high.iloc[-2] if long_breakout else lowest_low.iloc[-2], 2),
                        "volume_confirmation": bool(df[volume_col].iloc[-1] > df[volume_col].rolling(20).mean().iloc[-1])
                    }
                })
        except Exception as e:
            logger.error(f"Error scanning Donchian breakout for {symbol}: {str(e)}")
            continue
    
    return signals


def scan_rsi_momentum(symbols: List[str], oversold: int = 30, midline: int = 50, overbought: int = 70) -> List[Dict]:
    """Scan for RSI momentum signals."""
    signals = []
    
    for symbol in symbols:
        try:

            df = get_market_data(symbol)

            if df is None or len(df) < 14:  # Need at least 14 bars for RSI
                continue
                
            # Handle column name case
            close_col = 'Close' if 'Close' in df.columns else 'close'
            open_col = 'Open' if 'Open' in df.columns else 'open'
            high_col = 'High' if 'High' in df.columns else 'high'
            low_col = 'Low' if 'Low' in df.columns else 'low'
            volume_col = 'Volume' if 'Volume' in df.columns else 'volume'

            rsi = compute_rsi(df[close_col])
            
            # Entry: RSI crosses above 30 and then above 50
            long_entry = (rsi.iloc[-3] < oversold and rsi.iloc[-2] > oversold and rsi.iloc[-1] > midline)
            
            # Exit: RSI crosses below 70 then below 50
            long_exit = (rsi.iloc[-3] > overbought and rsi.iloc[-2] < overbought and rsi.iloc[-1] < midline)
            
            if long_entry:
                signals.append({
                    "symbol": symbol,
                    "algorithm": "RSI Momentum (30→50)",
                    "signal": "BUY",
                    "entry_price": df[close_col].iloc[-1],
                    "confidence": "Medium",
                    "timeframe": "1D",
                    "setup_details": {
                        "rsi_current": round(rsi.iloc[-1], 2),
                        "rsi_previous": round(rsi.iloc[-2], 2),
                        "momentum_confirmed": True,
                        "exit_target": "RSI 70→50"
                    }
                })
        except Exception as e:
            logger.error(f"Error scanning RSI momentum for {symbol}: {str(e)}")
            continue
    
    return signals


def scan_volume_spike(symbols: List[str], volume_multiplier: float = 2.0, limit: int = None) -> List[Dict]:
    """Scan for volume spike + price momentum."""
    signals = []
    
    for symbol in symbols:
        try:
            df = get_market_data(symbol)

            if df is None or len(df) < 20:
                continue
                
            # Use standardized lowercase column names
            close_col = 'close'
            open_col = 'open'
            volume_col = 'volume'
            
            # Check for zero volume (skip delisted/invalid tickers)
            if df[volume_col].iloc[-1] == 0:
                logger.warning(f"{symbol}: zero volume—skipping")
                continue
            
            avg_volume = df[volume_col].rolling(20).mean()
            
            # Volume spike with price up
            volume_spike = df[volume_col].iloc[-1] > (avg_volume.iloc[-1] * volume_multiplier)
            price_up = df[close_col].iloc[-1] > df[open_col].iloc[-1]
            
            if volume_spike and price_up:
                signals.append({
                    "symbol": symbol,
                    "algorithm": f"Volume Spike ({volume_multiplier}x)",
                    "signal": "BUY",
                    "entry_price": df[close_col].iloc[-1],
                    "confidence": "High",
                    "timeframe": "1D",
                    "setup_details": {
                        "volume_current": df[volume_col].iloc[-1],
                        "volume_average": int(avg_volume.iloc[-1]),
                        "volume_ratio": round(df[volume_col].iloc[-1] / avg_volume.iloc[-1], 2),
                        "price_action": "Strong up day"
                    }
                })
                
                # Apply limit if specified
                if limit and len(signals) >= limit:
                    break
        except Exception as e:
            logger.error(f"Error scanning volume spike for {symbol}: {str(e)}")
            continue
    
    return signals[:limit] if limit else signals


# =============================================================================
# OPTIONS STRATEGIES
# =============================================================================

def scan_long_straddle_setups(symbols: List[str], iv_threshold: float = 0.5) -> List[Dict]:
    """Scan for long straddle opportunities (high IV expected to decrease)."""
    signals = []
    
    for symbol in symbols:
        # Simulate options chain data
        current_price = 100  # Would fetch real price
        atm_strike = round(current_price / 5) * 5  # Round to nearest $5
        
        # Simulate IV data (in production, fetch from options API)
        call_iv = float(np.random.uniform(0.3, 0.8))
        put_iv = float(np.random.uniform(0.3, 0.8))
        
        if call_iv > iv_threshold and put_iv > iv_threshold:
            signals.append({
                "symbol": symbol,
                "algorithm": "Long Straddle (High IV)",
                "strategy_type": "options",
                "signal": "STRADDLE",
                "strike": atm_strike,
                "expiry": "2024-12-20",  # Example expiry
                "confidence": "Medium",
                "setup_details": {
                    "call_iv": round(call_iv, 3),
                    "put_iv": round(put_iv, 3),
                    "iv_rank": "High",
                    "expected_move": round(current_price * call_iv * 0.25, 2),  # Simplified calculation
                    "profit_zones": f"Below {atm_strike - 10} or above {atm_strike + 10}"
                }
            })
    
    return signals


def scan_iron_condor_setups(symbols: List[str], iv_rank_min: float = 50) -> List[Dict]:
    """Scan for iron condor opportunities (high IV expected to decrease)."""
    signals = []

    for symbol in symbols[:10]:  # Limit for performance
        try:
            # Try to get real data first
            df = get_market_data(symbol)
            if df is not None and len(df) > 20:
                close_col = 'Close' if 'Close' in df.columns else 'close'
                current_price = df[close_col].iloc[-1]

                # Calculate historical volatility
                returns = df[close_col].pct_change().dropna()
                hv = returns.std() * np.sqrt(252) * 100  # Annualized HV

                # Check if we have enough price movement for options
                price_range = (df[close_col].max() - df[close_col].min()) / df[close_col].mean()

            else:
                # Fallback to simulated data
                current_price = 100 + np.random.random() * 200
                hv = np.random.uniform(15, 45)
                price_range = 0.15

            # Simulate IV rank (in production, fetch from options API)
            # Use HV as proxy for IV rank
            iv_rank = min(95, max(30, hv * 2))  # Convert HV to IV rank proxy

            # More lenient criteria for demo
            if iv_rank > iv_rank_min and current_price > 20:  # Minimum price for options
                # Calculate optimal strikes based on expected move
                expected_move = current_price * (hv / 100) * np.sqrt(30/365)  # 30-day expected move

                # Iron condor strikes with 1 SD probability
                short_call = round(current_price + expected_move, 0)
                long_call = round(current_price + expected_move * 1.5, 0)
                short_put = round(current_price - expected_move, 0)
                long_put = round(current_price - expected_move * 1.5, 0)

                # Calculate max profit/loss
                strike_width = long_call - short_call
                credit = strike_width * 0.35  # Simulated credit (35% of width)
                max_loss = strike_width - credit

                signals.append({
                    "symbol": symbol,
                    "algorithm": "Iron Condor Strategy",
                    "strategy_type": "options",
                    "signal": "SELL_PREMIUM",
                    "confidence": "High" if iv_rank > 75 else "Medium",
                    "setup_details": {
                        "current_price": round(current_price, 2),
                        "iv_rank": round(iv_rank, 1),
                        "historical_volatility": round(hv, 1),
                        "short_call_strike": short_call,
                        "long_call_strike": long_call,
                        "short_put_strike": short_put,
                        "long_put_strike": long_put,
                        "expected_move": round(expected_move, 2),
                        "max_profit": round(credit * 100, 2),
                        "max_loss": round(max_loss * 100, 2),
                        "profit_probability": round(68.2, 1),  # 1 SD probability
                        "profit_zone": f"Between ${short_put} and ${short_call}",
                        "days_to_expiry": 30
                    }
                })
        except Exception as e:
            logger.error(f"Error scanning iron condor for {symbol}: {str(e)}")
            continue
    
    return signals


def scan_iron_butterfly_setups(symbols: List[str], iv_rank_min: float = 75) -> List[Dict]:
    """Scan for iron butterfly opportunities (very high IV, expecting minimal movement)."""
    signals = []
    
    for symbol in symbols:
        try:

            df = get_market_data(symbol)

            if df is None or len(df) < 20:
                continue
                
            # Handle column name case
            close_col = 'Close' if 'Close' in df.columns else 'close'
            open_col = 'Open' if 'Open' in df.columns else 'open'
            high_col = 'High' if 'High' in df.columns else 'high'
            low_col = 'Low' if 'Low' in df.columns else 'low'
            volume_col = 'Volume' if 'Volume' in df.columns else 'volume'

            current_price = df[close_col].iloc[-1]
        
            # Calculate metrics
            returns = df[close_col].pct_change().dropna()
            hv = returns.std() * np.sqrt(252) * 100
        
            # Check for low realized volatility but high IV rank
            iv_rank = float(np.random.uniform(70, 95))
        
            if iv_rank > iv_rank_min and hv < 30:  # Low HV, high IV
                # ATM strikes for butterfly
                atm_strike = round(current_price)
                wing_width = round(current_price * 0.05)  # 5% wings
            
                signals.append({
                    "symbol": symbol,
                    "algorithm": "Iron Butterfly (High IV, Low Movement)",
                    "strategy_type": "options",
                    "signal": "SELL_PREMIUM",
                    "confidence": "High",
                    "setup_details": {
                        "current_price": round(current_price, 2),
                        "iv_rank": round(iv_rank, 1),
                        "historical_volatility": round(hv, 1),
                        "short_strike": atm_strike,
                        "long_call_strike": atm_strike + wing_width,
                        "long_put_strike": atm_strike - wing_width,
                        "max_profit": round(wing_width * 0.4 * 100, 2),
                        "max_loss": round(wing_width * 0.6 * 100, 2),
                        "profit_zone": f"${atm_strike - wing_width/2} to ${atm_strike + wing_width/2}",
                        "ideal_scenario": "Stock stays near current price"
                    }
                })
        except Exception as e:
            logger.error(f"Error scanning iron butterfly for {symbol}: {str(e)}")
            continue
    
    return signals


def scan_calendar_spread_setups(symbols: List[str], iv_skew_min: float = 10) -> List[Dict]:
    """Scan for calendar spread opportunities (IV term structure plays)."""
    signals = []
    
    for symbol in symbols:
        try:

            df = get_market_data(symbol)

            if df is None or len(df) < 20:
                continue
                
            # Handle column name case
            close_col = 'Close' if 'Close' in df.columns else 'close'
            open_col = 'Open' if 'Open' in df.columns else 'open'
            high_col = 'High' if 'High' in df.columns else 'high'
            low_col = 'Low' if 'Low' in df.columns else 'low'
            volume_col = 'Volume' if 'Volume' in df.columns else 'volume'

            current_price = df[close_col].iloc[-1]
        
            # Simulate IV term structure
            front_month_iv = float(np.random.uniform(20, 60))
            back_month_iv = float(np.random.uniform(25, 65))
            iv_skew = back_month_iv - front_month_iv
        
            if iv_skew > iv_skew_min:
                atm_strike = round(current_price)
            
                signals.append({
                    "symbol": symbol,
                    "algorithm": "Calendar Spread (IV Skew)",
                    "strategy_type": "options",
                    "signal": "VOLATILITY_ARB",
                    "confidence": "Medium",
                    "setup_details": {
                        "current_price": round(current_price, 2),
                        "strike": atm_strike,
                        "front_month_iv": round(front_month_iv, 1),
                        "back_month_iv": round(back_month_iv, 1),
                        "iv_skew": round(iv_skew, 1),
                        "sell_expiry": "30 days",
                        "buy_expiry": "60 days",
                        "ideal_scenario": "Stock stays near strike, front month IV drops"
                    }
                })
        except Exception as e:
            logger.error(f"Error scanning calendar spread for {symbol}: {str(e)}")
            continue
    
    return signals


def scan_diagonal_spread_setups(symbols: List[str], trend_strength: float = 0.6) -> List[Dict]:
    """Scan for diagonal spread opportunities (directional with time decay)."""
    signals = []
    
    for symbol in symbols:
        try:

            df = get_market_data(symbol)

            if df is None or len(df) < 20:
                continue
                
            # Handle column name case
            close_col = 'Close' if 'Close' in df.columns else 'close'
            open_col = 'Open' if 'Open' in df.columns else 'open'
            high_col = 'High' if 'High' in df.columns else 'high'
            low_col = 'Low' if 'Low' in df.columns else 'low'
            volume_col = 'Volume' if 'Volume' in df.columns else 'volume'

            current_price = df[close_col].iloc[-1]
        
            # Calculate trend
            sma_20 = df[close_col].rolling(20).mean().iloc[-1]
            sma_50 = df[close_col].rolling(50).mean().iloc[-1]
            trend_score = (current_price - sma_50) / sma_50
        
            if abs(trend_score) > trend_strength / 100:
                bullish = trend_score > 0
            
                signals.append({
                    "symbol": symbol,
                    "algorithm": "Diagonal Spread",
                    "strategy_type": "options",
                    "signal": "BULLISH_DIAGONAL" if bullish else "BEARISH_DIAGONAL",
                    "confidence": "Medium",
                    "setup_details": {
                        "current_price": round(current_price, 2),
                        "trend": "Bullish" if bullish else "Bearish",
                        "trend_strength": round(abs(trend_score) * 100, 1),
                        "short_strike": round(current_price * (1.05 if bullish else 0.95)),
                        "long_strike": round(current_price * (1.02 if bullish else 0.98)),
                        "short_expiry": "30 days",
                        "long_expiry": "60 days",
                        "strategy": f"Sell near-term {'call' if bullish else 'put'}, buy longer-term"
                    }
                })
        except Exception as e:
            logger.error(f"Error scanning diagonal spread for {symbol}: {str(e)}")
            continue
    
    return signals


def scan_covered_call_setups(symbols: List[str], min_premium: float = 2.0) -> List[Dict]:
    """Scan for covered call opportunities on stocks we own or want to own."""
    signals = []
    
    for symbol in symbols:
        try:

            df = get_market_data(symbol)

            if df is None or len(df) < 20:
                continue
                
            # Handle column name case
            close_col = 'Close' if 'Close' in df.columns else 'close'
            open_col = 'Open' if 'Open' in df.columns else 'open'
            high_col = 'High' if 'High' in df.columns else 'high'
            low_col = 'Low' if 'Low' in df.columns else 'low'
            volume_col = 'Volume' if 'Volume' in df.columns else 'volume'

            current_price = df[close_col].iloc[-1]
        
            # Calculate metrics
            returns = df[close_col].pct_change().dropna()
            hv = returns.std() * np.sqrt(252) * 100
        
            # Simulate call premium
            otm_strike = round(current_price * 1.05)  # 5% OTM
            call_premium = current_price * (hv / 100) * np.sqrt(30/365) * 0.4
            premium_yield = (call_premium / current_price) * 100
        
            if premium_yield > min_premium:
                signals.append({
                    "symbol": symbol,
                    "algorithm": "Covered Call",
                    "strategy_type": "options",
                    "signal": "INCOME_GENERATION",
                    "confidence": "High",
                    "setup_details": {
                        "current_price": round(current_price, 2),
                        "call_strike": otm_strike,
                        "call_premium": round(call_premium, 2),
                        "premium_yield": round(premium_yield, 2),
                        "if_called_return": round(((otm_strike - current_price + call_premium) / current_price) * 100, 2),
                        "breakeven": round(current_price - call_premium, 2),
                        "days_to_expiry": 30,
                        "ideal_scenario": f"Stock stays below ${otm_strike}"
                    }
                })
        except Exception as e:
            logger.error(f"Error scanning covered call for {symbol}: {str(e)}")
            continue
    
    return signals


def scan_cash_secured_put_setups(symbols: List[str], min_premium: float = 2.0) -> List[Dict]:
    """Scan for cash-secured put opportunities to acquire stocks at discount."""
    signals = []
    
    for symbol in symbols:
        try:

            df = get_market_data(symbol)

            if df is None or len(df) < 20:
                continue
                
            # Handle column name case
            close_col = 'Close' if 'Close' in df.columns else 'close'
            open_col = 'Open' if 'Open' in df.columns else 'open'
            high_col = 'High' if 'High' in df.columns else 'high'
            low_col = 'Low' if 'Low' in df.columns else 'low'
            volume_col = 'Volume' if 'Volume' in df.columns else 'volume'

            current_price = df[close_col].iloc[-1]
        
            # Support level analysis
            support = df[low_col].rolling(20).min().iloc[-1]
        
            # Put strike near support
            put_strike = round(support * 1.02)  # Slightly above support
        
            # Simulate put premium
            returns = df[close_col].pct_change().dropna()
            hv = returns.std() * np.sqrt(252) * 100
            put_premium = current_price * (hv / 100) * np.sqrt(30/365) * 0.35
            premium_yield = (put_premium / put_strike) * 100
        
            if premium_yield > min_premium and put_strike < current_price * 0.95:
                signals.append({
                    "symbol": symbol,
                    "algorithm": "Cash-Secured Put",
                    "strategy_type": "options",
                    "signal": "ACQUIRE_STOCK",
                    "confidence": "High",
                    "setup_details": {
                        "current_price": round(current_price, 2),
                        "put_strike": put_strike,
                        "put_premium": round(put_premium, 2),
                        "premium_yield": round(premium_yield, 2),
                        "effective_purchase_price": round(put_strike - put_premium, 2),
                        "discount_to_current": round(((current_price - (put_strike - put_premium)) / current_price) * 100, 2),
                        "support_level": round(support, 2),
                        "days_to_expiry": 30,
                        "ideal_scenario": f"Acquire stock at ${put_strike} or keep premium"
                    }
                })
        except Exception as e:
            logger.error(f"Error scanning cash secured put for {symbol}: {str(e)}")
            continue
    
    return signals


def scan_vertical_spread_setups(symbols: List[str], min_risk_reward: float = 2.0) -> List[Dict]:
    """Scan for vertical spread opportunities (bull/bear call/put spreads)."""
    signals = []
    
    for symbol in symbols:
        try:

            df = get_market_data(symbol)

            if df is None or len(df) < 20:
                continue
                
            # Handle column name case
            close_col = 'Close' if 'Close' in df.columns else 'close'
            open_col = 'Open' if 'Open' in df.columns else 'open'
            high_col = 'High' if 'High' in df.columns else 'high'
            low_col = 'Low' if 'Low' in df.columns else 'low'
            volume_col = 'Volume' if 'Volume' in df.columns else 'volume'

            current_price = df[close_col].iloc[-1]
        
            # Trend analysis
            ema_9 = compute_ema(df[close_col], 9).iloc[-1]
            ema_21 = compute_ema(df[close_col], 21).iloc[-1]
            rsi = compute_rsi(df[close_col]).iloc[-1]
        
            bullish = ema_9 > ema_21 and rsi > 50
            bearish = ema_9 < ema_21 and rsi < 50
        
            if bullish or bearish:
                # Calculate spread strikes
                if bullish:
                    long_strike = round(current_price * 1.01)  # Slightly ITM
                    short_strike = round(current_price * 1.05)  # OTM
                    spread_type = "Bull Call Spread"
                else:
                    long_strike = round(current_price * 0.99)  # Slightly ITM
                    short_strike = round(current_price * 0.95)  # OTM
                    spread_type = "Bear Put Spread"
            
                # Simulate risk/reward
                max_profit = abs(short_strike - long_strike)
                cost = max_profit * 0.3  # Simulated debit
                risk_reward = max_profit / cost
            
                if risk_reward >= min_risk_reward:
                    signals.append({
                        "symbol": symbol,
                        "algorithm": "Vertical Spread",
                        "strategy_type": "options",
                        "signal": spread_type.upper().replace(" ", "_"),
                        "confidence": "High" if risk_reward > 3 else "Medium",
                        "setup_details": {
                            "current_price": round(current_price, 2),
                            "spread_type": spread_type,
                            "long_strike": long_strike,
                            "short_strike": short_strike,
                            "max_profit": round(max_profit * 100, 2),
                            "max_loss": round(cost * 100, 2),
                            "risk_reward_ratio": round(risk_reward, 2),
                            "breakeven": round(long_strike + cost if bullish else long_strike - cost, 2),
                            "probability_profit": round(65 if risk_reward > 2.5 else 55, 1),
                            "days_to_expiry": 30
                        }
                    })
        except Exception as e:
            logger.error(f"Error scanning vertical spread for {symbol}: {str(e)}")
            continue
    
    return signals


def scan_ratio_spread_setups(symbols: List[str], iv_rank_min: float = 60) -> List[Dict]:
    """Scan for ratio spread opportunities (1x2, 2x3 ratios)."""
    signals = []
    
    for symbol in symbols:
        try:

            df = get_market_data(symbol)

            if df is None or len(df) < 20:
                continue
                
            # Handle column name case
            close_col = 'Close' if 'Close' in df.columns else 'close'
            open_col = 'Open' if 'Open' in df.columns else 'open'
            high_col = 'High' if 'High' in df.columns else 'high'
            low_col = 'Low' if 'Low' in df.columns else 'low'
            volume_col = 'Volume' if 'Volume' in df.columns else 'volume'

            current_price = df[close_col].iloc[-1]
        
            # IV analysis
            iv_rank = float(np.random.uniform(50, 90))
        
            if iv_rank > iv_rank_min:
                # Setup 1x2 call ratio
                long_strike = round(current_price)
                short_strike = round(current_price * 1.05)
            
                signals.append({
                    "symbol": symbol,
                    "algorithm": "Ratio Spread (1x2)",
                    "strategy_type": "options",
                    "signal": "RATIO_SPREAD",
                    "confidence": "Medium",
                    "setup_details": {
                        "current_price": round(current_price, 2),
                        "iv_rank": round(iv_rank, 1),
                        "long_strike": long_strike,
                        "long_contracts": 1,
                        "short_strike": short_strike,
                        "short_contracts": 2,
                        "credit_received": round((short_strike - long_strike) * 0.2 * 100, 2),
                        "max_profit_range": f"${long_strike} to ${short_strike}",
                        "risk": "Unlimited above breakeven",
                        "ideal_scenario": "Moderate upward movement"
                    }
                })
        except Exception as e:
            logger.error(f"Error scanning ratio spread for {symbol}: {str(e)}")
            continue
    
    return signals


# =============================================================================
# INTELLIGENT STRATEGY SELECTION
# =============================================================================

def get_best_strategy_for_conditions(market_conditions: Dict) -> Dict:
    """
    Intelligently select the best trading strategy based on current market conditions.
    
    Args:
        market_conditions: Dict containing volatility, trend, volume, etc.
        
    Returns:
        Dict with recommended strategies and reasoning
    """
    recommendations = []
    
    # Extract conditions
    volatility = market_conditions.get("volatility", "normal")
    trend = market_conditions.get("trend", "neutral")
    volume = market_conditions.get("volume", "average")
    iv_rank = market_conditions.get("iv_rank", 50)
    market_phase = market_conditions.get("market_phase", "regular")
    
    # High volatility conditions
    if volatility == "high" or iv_rank > 70:
        recommendations.extend([
            {
                "strategy": "Iron Condor",
                "reason": f"High IV rank ({iv_rank}%) - sell premium while IV is elevated",
                "confidence": "High",
                "expected_win_rate": 68
            },
            {
                "strategy": "Iron Butterfly", 
                "reason": "Very high IV with expected range-bound movement",
                "confidence": "High" if iv_rank > 80 else "Medium",
                "expected_win_rate": 65
            },
            {
                "strategy": "Cash-Secured Put",
                "reason": "Collect high premium or acquire stock at discount",
                "confidence": "High",
                "expected_win_rate": 75
            }
        ])
    
    # Low volatility conditions
    elif volatility == "low" or iv_rank < 30:
        recommendations.extend([
            {
                "strategy": "Calendar Spread",
                "reason": "Low IV - benefit from volatility expansion",
                "confidence": "Medium",
                "expected_win_rate": 60
            },
            {
                "strategy": "Long Straddle",
                "reason": "Low IV - cheap options for big move plays",
                "confidence": "Medium",
                "expected_win_rate": 45
            }
        ])
    
    # Strong trending conditions
    if trend in ["strong_bullish", "strong_bearish"]:
        direction = "Bull" if "bullish" in trend else "Bear"
        recommendations.extend([
            {
                "strategy": f"{direction} Call/Put Spread",
                "reason": f"Strong {direction.lower()}ish trend - directional play with defined risk",
                "confidence": "High",
                "expected_win_rate": 65
            },
            {
                "strategy": "Diagonal Spread",
                "reason": f"Trending market - capture directional move with time decay",
                "confidence": "Medium",
                "expected_win_rate": 60
            },
            {
                "strategy": "Donchian Breakout",
                "reason": "Strong trend - ride momentum with trailing stops",
                "confidence": "High",
                "expected_win_rate": 55
            }
        ])
    
    # Range-bound conditions
    elif trend == "neutral" or market_phase == "consolidation":
        recommendations.extend([
            {
                "strategy": "Bollinger Mean Reversion",
                "reason": "Range-bound market - fade extremes back to mean",
                "confidence": "High",
                "expected_win_rate": 70
            },
            {
                "strategy": "Iron Butterfly",
                "reason": "Neutral market - profit from time decay",
                "confidence": "Medium",
                "expected_win_rate": 65
            }
        ])
    
    # High volume conditions
    if volume == "high":
        recommendations.append({
            "strategy": "Volume Spike Momentum",
            "reason": "Unusual volume - potential breakout or news-driven move",
            "confidence": "High",
            "expected_win_rate": 60
        })
    
    # TTM Squeeze conditions
    if market_conditions.get("ttm_squeeze", False):
        recommendations.append({
            "strategy": "TTM Squeeze",
            "reason": "Volatility compression - explosive move expected",
            "confidence": "High",
            "expected_win_rate": 65
        })
    
    # Sort by confidence and win rate
    recommendations.sort(key=lambda x: (
        {"High": 3, "Medium": 2, "Low": 1}[x["confidence"]],
        x["expected_win_rate"]
    ), reverse=True)
    
    return {
        "top_recommendations": recommendations[:3],
        "market_conditions": market_conditions,
        "analysis_timestamp": datetime.now().isoformat()
    }


def analyze_market_conditions(symbol: str) -> Dict:
    """
    Analyze current market conditions for a symbol.
    
    Returns:
        Dict with volatility, trend, volume analysis
    """
    try:
        df = get_market_data(symbol, limit=50)
        if df is None or len(df) < 20:
            return {
                "symbol": symbol,
                "volatility": "normal",
                "trend": "neutral",
                "volume": "average",
                "error": "Insufficient data"
            }
            
        # Handle column name case
        close_col = 'Close' if 'Close' in df.columns else 'close'
        open_col = 'Open' if 'Open' in df.columns else 'open'
        high_col = 'High' if 'High' in df.columns else 'high'
        low_col = 'Low' if 'Low' in df.columns else 'low'
        volume_col = 'Volume' if 'Volume' in df.columns else 'volume'

        
        # Calculate metrics
        returns = df[close_col].pct_change().dropna()
        current_vol = returns.std() * np.sqrt(252) * 100
        
        # Volatility classification
        if current_vol > 40:
            volatility = "high"
        elif current_vol < 20:
            volatility = "low"
        else:
            volatility = "normal"
        
        # Trend analysis
        sma_20 = df[close_col].rolling(20).mean().iloc[-1]
        sma_50 = df[close_col].rolling(50).mean().iloc[-1] if len(df) >= 50 else sma_20
        current_price = df[close_col].iloc[-1]
        
        if current_price > sma_20 * 1.02 and sma_20 > sma_50:
            trend = "strong_bullish"
        elif current_price < sma_20 * 0.98 and sma_20 < sma_50:
            trend = "strong_bearish"
        elif abs(current_price - sma_20) / sma_20 < 0.01:
            trend = "neutral"
        else:
            trend = "weak_bullish" if current_price > sma_20 else "weak_bearish"
        
        # Volume analysis
        avg_volume = df[volume_col].rolling(20).mean().iloc[-1]
        current_volume = df[volume_col].iloc[-1]
        
        if current_volume > avg_volume * 1.5:
            volume = "high"
        elif current_volume < avg_volume * 0.5:
            volume = "low"
        else:
            volume = "average"
        
        # Check for squeeze
        df_squeeze = calculate_ttm_squeeze(df)
        in_squeeze = df_squeeze['squeeze_on'].iloc[-1] if 'squeeze_on' in df_squeeze.columns else False
        
        return {
            "symbol": symbol,
            "volatility": volatility,
            "current_volatility": round(current_vol, 1),
            "trend": trend,
            "volume": volume,
            "volume_ratio": round(current_volume / avg_volume, 2),
            "price": round(current_price, 2),
            "sma_20": round(sma_20, 2),
            "iv_rank": float(np.random.uniform(20, 80)),  # Simulated, would use real options data
            "ttm_squeeze": in_squeeze,
            "market_phase": "consolidation" if trend == "neutral" else "trending"
        }
        
    except Exception as e:
        logger.error(f"Error analyzing market conditions for {symbol}: {e}")
        return {
            "symbol": symbol,
            "volatility": "normal",
            "trend": "neutral",
            "volume": "average",
            "error": str(e)
        }


# =============================================================================
# CRYPTO STRATEGIES
# =============================================================================

def scan_crypto_macd(symbols: List[str], fast: int = 12, slow: int = 26, signal: int = 9) -> List[Dict]:
    """Scan crypto for MACD crossover signals."""
    signals = []

    for symbol in symbols:
        try:
            df = get_market_data(symbol)
            if df is None or len(df) < 26:
                logger.warning(f"Insufficient data for {symbol}: {len(df) if df is not None else 0} bars")
                continue

            # Handle column name case
            close_col = 'Close' if 'Close' in df.columns else 'close'

            macd_line, signal_line, histogram = compute_macd(df[close_col], fast, slow, signal)

            # MACD bullish crossover
            macd_cross_up = (macd_line.iloc[-2] <= signal_line.iloc[-2] and
                            macd_line.iloc[-1] > signal_line.iloc[-1])

            # Also check for MACD momentum (histogram increasing)
            macd_momentum = histogram.iloc[-1] > histogram.iloc[-2]

            # More lenient conditions for demo purposes
            if macd_cross_up or (macd_momentum and macd_line.iloc[-1] > signal_line.iloc[-1]):
                confidence = "High" if macd_cross_up else "Medium"
                signal_type = "Bullish crossover" if macd_cross_up else "Momentum building"

                signals.append({
                    "symbol": symbol,
                    "algorithm": f"Crypto MACD Analysis ({fast},{slow},{signal})",
                    "signal": "BUY",
                    "entry_price": round(df[close_col].iloc[-1], 2),
                    "confidence": confidence,
                    "timeframe": "4H",
                    "setup_details": {
                        "macd": round(macd_line.iloc[-1], 4),
                        "signal_line": round(signal_line.iloc[-1], 4),
                        "histogram": round(histogram.iloc[-1], 4),
                        "histogram_change": round(histogram.iloc[-1] - histogram.iloc[-2], 4),
                        "trend": signal_type,
                        "price_change_24h": round((df[close_col].iloc[-1] / df[close_col].iloc[-2] - 1) * 100, 2)
                    }
                })
        except Exception as e:
            logger.error(f"Error scanning crypto MACD for {symbol}: {str(e)}")
            continue

    return signals


def scan_crypto_rsi_oversold(symbols: List[str], oversold: int = 30, overbought: int = 70) -> List[Dict]:
    """Scan crypto for RSI oversold/overbought conditions."""
    signals = []
    
    for symbol in symbols:
        df = get_market_data(symbol)
        if df is None or len(df) < 14:
            continue
            
        # Handle column name case
        close_col = 'Close' if 'Close' in df.columns else 'close'
        
        rsi = compute_rsi(df[close_col])
        
        if rsi.iloc[-1] < oversold:
            signals.append({
                "symbol": symbol,
                "algorithm": "Crypto RSI Oversold",
                "signal": "BUY",
                "entry_price": df[close_col].iloc[-1],
                "confidence": "Medium",
                "timeframe": "1H",
                "setup_details": {
                    "rsi": round(rsi.iloc[-1], 2),
                    "condition": "Oversold",
                    "target_rsi": 50,
                    "expected_bounce": "Mean reversion play"
                }
            })
        elif rsi.iloc[-1] > overbought:
            signals.append({
                "symbol": symbol,
                "algorithm": "Crypto RSI Overbought",
                "signal": "SELL",
                "entry_price": df[close_col].iloc[-1],
                "confidence": "Medium",
                "timeframe": "1H",
                "setup_details": {
                    "rsi": round(rsi.iloc[-1], 2),
                    "condition": "Overbought",
                    "target_rsi": 50,
                    "expected_correction": "Mean reversion play"
                }
            })
    
    return signals


def scan_crypto_onchain_nvt(symbols: List[str]) -> List[Dict]:
    """Scan for on-chain NVT signals (Network Value to Transactions)."""
    signals = []
    
    # Crypto-specific symbols only
    crypto_symbols = [s for s in symbols if 'USD' in s and any(crypto in s for crypto in ['BTC', 'ETH', 'ADA', 'SOL'])]
    
    for symbol in crypto_symbols:
        # Simulate NVT data (in production, fetch from on-chain APIs)
        nvt_current = float(np.random.uniform(20, 150))
        nvt_ma_30 = float(np.random.uniform(40, 120))
        
        # Signal when NVT drops below its 30-day MA (bullish)
        if nvt_current < nvt_ma_30:
            signals.append({
                "symbol": symbol,
                "algorithm": "On-Chain NVT Signal",
                "signal": "BUY",
                "confidence": "High",
                "timeframe": "1D",
                "setup_details": {
                    "nvt_current": round(nvt_current, 1),
                    "nvt_ma_30": round(nvt_ma_30, 1),
                    "interpretation": "Network utility improving relative to valuation",
                    "on_chain_health": "Bullish"
                }
            })
    
    return signals


# =============================================================================
# FUNDAMENTAL & EVENT-DRIVEN STRATEGIES
# =============================================================================

def scan_insider_buying(symbols: List[str] = None, days: int = 30) -> List[Dict]:
    """Scan for insider buying activity."""
    signals = []
    
    # If no symbols provided, use some popular stocks
    if not symbols:
        symbols = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'JPM', 'BAC', 'WFC']
    
    # For now, return simulated insider buying signals
    # In production, this would connect to real insider data
    for i, symbol in enumerate(symbols[:5]):  # Limit to 5 for demo
        if np.random.random() > 0.5:  # 50% chance of insider activity
            buy_value = np.random.randint(100000, 5000000)
            sell_value = np.random.randint(0, buy_value // 2)
            net_value = buy_value - sell_value
            
            signals.append({
                "symbol": symbol,
                "algorithm": "Insider Net Buying",
                "signal": "BUY",
                "confidence": "Very High" if net_value > 1000000 else "High",
                "timeframe": "Long-term",
                "entry_price": 100 + np.random.random() * 50,  # Simulated price
                "setup_details": {
                    "insider_buys": np.random.randint(2, 8),
                    "insider_sells": np.random.randint(0, 3),
                    "net_buy_value": net_value,
                    "buy_value": buy_value,
                    "sell_value": sell_value,
                    "recent_buyers": ["CEO", "CFO", "Director"][: np.random.randint(1, 4)],
                    "most_recent_trade": datetime.now().strftime("%Y-%m-%d"),
                    "analysis_period": f"{days} days",
                    "interpretation": f"Strong insider accumulation: ${net_value:,.0f} net buying"
                }
            })
    
    return signals


def scan_analyst_upgrades(symbols: List[str] = None, lookback_days: int = 7) -> List[Dict]:
    """Scan for recent analyst upgrades."""
    signals = []
    
    # If no symbols provided, use some popular stocks
    if not symbols:
        symbols = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'JPM', 'BAC', 'WFC']
    
    for symbol in symbols[:10]:  # Limit to 10 for performance
        # Simulate analyst activity
        upgrades = int(np.random.randint(0, 5))
        downgrades = int(np.random.randint(0, 3))
        
        if upgrades > downgrades and upgrades >= 2:
            # Generate realistic price targets
            current_price = 100 + np.random.random() * 200
            avg_target = current_price * (1 + np.random.uniform(0.05, 0.25))
            
            signals.append({
                "symbol": symbol,
                "algorithm": "Analyst Upgrade Flow",
                "signal": "BUY",
                "confidence": "High" if upgrades >= 3 else "Medium",
                "timeframe": "Medium-term",
                "entry_price": current_price,
                "setup_details": {
                    "upgrades": upgrades,
                    "downgrades": downgrades,
                    "net_sentiment": "Positive",
                    "avg_price_target": round(avg_target, 2),
                    "upside_potential": round((avg_target / current_price - 1) * 100, 1),
                    "recent_firms": ["Goldman Sachs", "Morgan Stanley", "JP Morgan", "Bank of America"][:upgrades],
                    "period": f"{lookback_days} days",
                    "momentum": "Analyst optimism increasing"
                }
            })
    
    return signals


def calculate_ttm_squeeze(
    df: pd.DataFrame,
    bb_length: int = 20,
    bb_std: float = 2.0,
    kc_length: int = 20,
    kc_mult: float = 1.5,
    hist_length: int = 12
) -> pd.DataFrame:
    """
    Adds TTM Squeeze columns to df:
      - sma, stddev, bb_upper, bb_lower
      - atr, kc_upper, kc_lower
      - squeeze_on (True when BB inside KC)
      - squeeze_off
      - ratio (bandwidth / (2*ATR))
      - hist (momentum histogram, via simple difference)
    
    Parameters:
    -----------
    df : pd.DataFrame
        Must contain 'high', 'low', 'close' columns (lowercase).
    bb_length : int
        Period for Bollinger Bands moving average and STD.
    bb_std : float
        Number of standard deviations for BB.
    kc_length : int
        Period for Keltner Channel ATR.
    kc_mult : float
        ATR multiplier for KC.
    hist_length : int
        Lookback for momentum histogram (difference of close).
    
    Returns:
    --------
    df : pd.DataFrame
        Original frame with new columns.
    """
    df = df.copy()
    
    # 1) Bollinger Bands
    df['sma']     = df['close'].rolling(bb_length).mean()                    # simple moving average
    df['stddev']  = df['close'].rolling(bb_length).std()                     # rolling standard deviation
    df['bb_upper'] = df['sma'] + bb_std * df['stddev']                       # upper band
    df['bb_lower'] = df['sma'] - bb_std * df['stddev']                       # lower band
    
    # 2) True Range & ATR for Keltner
    tr0 = df['high'] - df['low']
    tr1 = (df['high'] - df['close'].shift()).abs()
    tr2 = (df['low']  - df['close'].shift()).abs()
    df['tr'] = pd.concat([tr0, tr1, tr2], axis=1).max(axis=1)               # true range
    df['atr'] = df['tr'].rolling(kc_length).mean()                          # average true range
    
    # 3) Keltner Channels
    df['kc_upper'] = df['sma'] + kc_mult * df['atr']                        # KC upper
    df['kc_lower'] = df['sma'] - kc_mult * df['atr']                        # KC lower
    
    # 4) Squeeze ON/OFF flags
    df['squeeze_on']  = (df['bb_lower'] > df['kc_lower']) & (df['bb_upper'] < df['kc_upper'])
    df['squeeze_off'] = ~df['squeeze_on']
    
    # 5) Squeeze ratio (bandwidth / (2*ATR))
    bandwidth = df['bb_upper'] - df['bb_lower']
    df['squeeze_ratio'] = bandwidth / (2 * df['atr'])
    
    # 6) Momentum histogram (difference over hist_length)
    df['hist'] = df['close'].diff(hist_length)                             # simple momentum
    
    # 7) Optionally color-code histogram for plotting downstream
    #    e.g. positive rising = yellow, negative falling = red, neutral = gray
    df['hist_color'] = np.where(
        df['hist'] > 0, 
        np.where(df['hist'] > df['hist'].shift(), 'yellow', 'gray'),
        np.where(df['hist'] < df['hist'].shift(), 'red', 'gray')
    )
    
    return df


def scan_ttm_squeeze(symbols: List[str], limit: int = 10) -> List[Dict]:
    """Scan for stocks showing TTM Squeeze setup with specific pattern:
    - 4 or more consecutive negative histogram bars followed by a yellow bar (still negative but less negative)
    - EMA 8 > EMA 21 and both rising
    - Momentum up (close > previous close)
    - Volume spike (> 1.5x 20-day average)
    - Squeeze must be on
    """
    if not symbols:
        symbols = get_sp500_symbols()[:50]  # Reduced to top 50 S&P stocks to avoid rate limits
    
    signals = []
    
    # Clean up symbols - remove any that might cause issues
    clean_symbols = []
    for sym in symbols:
        # Skip problematic symbols
        if sym in ['ANTM', 'FB', 'TWTR']:  # These have been renamed
            continue
        # Fix BRK symbols
        if sym == 'BRK-B':
            clean_symbols.append('BRK.B')
        elif sym == 'BRK-A':
            clean_symbols.append('BRK.A')
        else:
            clean_symbols.append(sym)
    
    for symbol in clean_symbols[:limit * 3]:  # Process 3x the limit to account for failures
        try:
            # Add small delay to avoid rate limiting
            time.sleep(0.1)
            
            # Get market data - need at least 50 bars for pattern detection
            df = get_market_data(symbol, timeframe='1Day', limit=60)
            if df is None or len(df) < 50:
                continue
            
            # Calculate TTM Squeeze
            df = calculate_ttm_squeeze(df)
            
            # Add the specific pattern detection
            # 1) Count consecutive negative histogram bars
            df['hist_neg'] = df['hist'] < 0
            
            # Rolling count of consecutive negatives ending on prior bar
            def count_consecutive_negatives(x):
                # Count consecutive True values from the end, excluding the last value
                if len(x) < 2:
                    return 0
                count = 0
                for i in range(len(x)-2, -1, -1):  # Start from second-to-last
                    if x[i]:
                        count += 1
                    else:
                        break
                return count
            
            df['neg_count'] = df['hist_neg'].rolling(window=10, min_periods=5).apply(
                count_consecutive_negatives, raw=False
            )
            
            # Flag when we have 4+ negatives then a less-negative bar (yellow bar)
            df['hist_pattern'] = (
                (df['neg_count'] >= 4) & 
                (df['hist'] < 0) & 
                (df['hist'] > df['hist'].shift(1))
            )
            
            # 2) EMA 8 & EMA 21 both rising, and 8 > 21
            df['ema8'] = compute_ema(df['close'], 8)
            df['ema21'] = compute_ema(df['close'], 21)
            df['ema_rising'] = (
                (df['ema8'] > df['ema8'].shift(1)) & 
                (df['ema21'] > df['ema21'].shift(1))
            )
            df['ema_cross'] = df['ema8'] > df['ema21']
            
            # 3) Momentum: close today > close yesterday
            df['momentum_up'] = df['close'] > df['close'].shift(1)
            
            # 4) Volume spike: today's volume > 1.5 × 20-bar avg
            vol_window = 20
            vol_mult = 1.5
            df['avg_vol'] = df['volume'].rolling(vol_window).mean()
            df['vol_spike'] = df['volume'] > df['avg_vol'] * vol_mult
            
            # 5) Squeeze must be on
            df['squeeze_ok'] = df['squeeze_on']
            
            # Final signal if all conditions are true
            df['signal'] = (
                df['hist_pattern'] &
                df['ema_rising'] &
                df['ema_cross'] &
                df['momentum_up'] &
                df['vol_spike'] &
                df['squeeze_ok']
            )
            
            # Check if we have a signal on the latest bar
            latest = df.iloc[-1]
            if latest['signal']:
                # Calculate additional metrics for the signal
                hist_bars_negative = int(latest['neg_count'])
                volume_ratio = latest['volume'] / latest['avg_vol']
                ema_separation = (latest['ema8'] - latest['ema21']) / latest['close'] * 100
                
                # Calculate stop loss and targets
                atr = latest['atr']
                entry_price = latest['close']
                stop_loss = entry_price - (2 * atr)  # 2 ATR stop
                target_1 = entry_price + (2 * atr)   # 2:1 R/R
                target_2 = entry_price + (3 * atr)   # 3:1 R/R
                
                signals.append({
                    "symbol": symbol,
                    "algorithm": "TTM Squeeze Pattern",
                    "signal": "BUY",
                    "entry_price": round(entry_price, 2),
                    "stop_loss": round(stop_loss, 2),
                    "target_1": round(target_1, 2),
                    "target_2": round(target_2, 2),
                    "confidence": "High" if hist_bars_negative >= 7 else "Medium",
                    "timeframe": "1D",
                    "setup_details": {
                        "pattern": f"{hist_bars_negative} red bars → yellow bar",
                        "squeeze_status": "Active",
                        "histogram_value": round(latest['hist'], 2),
                        "histogram_change": round(latest['hist'] - df.iloc[-2]['hist'], 2),
                        "ema8": round(latest['ema8'], 2),
                        "ema21": round(latest['ema21'], 2),
                        "ema_separation": round(ema_separation, 2),
                        "volume_spike": round(volume_ratio, 2),
                        "atr": round(atr, 2),
                        "risk_reward_1": "1:2",
                        "risk_reward_2": "1:3",
                        "squeeze_ratio": round(latest['squeeze_ratio'], 2),
                        "current_price": round(latest['close'], 2),
                        "ema_trend": "Bullish (8 > 21, both rising)",
                        "interpretation": f"TTM Squeeze with {hist_bars_negative} consecutive red bars turning yellow. "
                                        f"Volume {volume_ratio:.1f}x average. EMAs aligned bullish."
                    }
                })
        
        except Exception as e:
            logger.error(f"Error scanning TTM Squeeze for {symbol}: {e}")
            continue
    
    # Sort by confidence and number of negative bars
    signals.sort(key=lambda x: (
        0 if x['confidence'] == 'High' else 1,
        -int(x['setup_details']['pattern'].split()[0])  # More negative bars = stronger setup
    ))
    
    return signals[:limit]


# =============================================================================
# EXECUTION FUNCTIONS
# =============================================================================

def execute_strategy_trade(
    symbol: str, 
    strategy: str, 
    signal: str, 
    qty: float, 
    entry_price: float,
    strategy_details: Dict
) -> Dict:
    """Execute REAL trades via Alpaca API with full metadata."""
    
    # Calculate risk management levels
    if signal == "BUY":
        stop_loss = entry_price * 0.98  # 2% stop
        take_profit = entry_price * 1.04  # 4% target
        side = "buy"
    else:
        stop_loss = entry_price * 1.02  # 2% stop  
        take_profit = entry_price * 0.96  # 4% target
        side = "sell"
    
    # Get Alpaca credentials
    alpaca_key = os.getenv("APCA_API_KEY_ID")
    alpaca_secret = os.getenv("APCA_API_SECRET_KEY")
    
    if not alpaca_key or not alpaca_secret:
        # Fallback to simulation if no credentials
        order_id = f"SIM_{strategy.upper()}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        return {
            "order": {
                "id": order_id,
                "symbol": symbol,
                "side": side,
                "qty": int(qty),
                "type": "market",
                "entry_price": entry_price,
                "stop_loss": stop_loss,
                "take_profit": take_profit,
                "status": "simulated_fill"
            },
            "algorithm": strategy,
            "strategy_details": {
                **strategy_details,
                "risk_reward": round(abs(take_profit - entry_price) / abs(entry_price - stop_loss), 2),
                "position_size_logic": f"{int(qty)} units based on 2% account risk",
                "execution_time": datetime.now().isoformat(),
                "execution_note": "Simulated - No Alpaca credentials"
            }
        }
    
    # Place REAL trade via Alpaca API
    try:
        alpaca_base = "https://paper-api.alpaca.markets"
        headers = {
            "APCA-API-KEY-ID": alpaca_key,
            "APCA-API-SECRET-KEY": alpaca_secret,
            "Content-Type": "application/json"
        }
        
        # Create bracket order (market entry + stop loss + take profit)
        order_payload = {
            "symbol": symbol,
            "qty": int(qty),
            "side": side,
            "type": "market",
            "time_in_force": "day",
            "order_class": "bracket",
            "take_profit": {
                "limit_price": round(take_profit, 2)
            },
            "stop_loss": {
                "stop_price": round(stop_loss, 2)
            }
        }
        
        # Submit order to Alpaca
        response = requests.post(
            f"{alpaca_base}/v2/orders",
            headers=headers,
            json=order_payload,
            timeout=45
        )
        
        if response.status_code == 201:
            # Real order placed successfully!
            order_data = response.json()
            return {
                "order": {
                    "id": order_data.get("id", "UNKNOWN"),
                    "symbol": symbol,
                    "side": side,
                    "qty": int(qty),
                    "type": "market",
                    "entry_price": entry_price,
                    "stop_loss": stop_loss,
                    "take_profit": take_profit,
                    "status": "submitted_to_alpaca",
                    "alpaca_order_id": order_data.get("id"),
                    "client_order_id": order_data.get("client_order_id")
                },
                "algorithm": strategy,
                "strategy_details": {
                    **strategy_details,
                    "risk_reward": round(abs(take_profit - entry_price) / abs(entry_price - stop_loss), 2),
                    "position_size_logic": f"{int(qty)} units based on 2% account risk",
                    "execution_time": datetime.now().isoformat(),
                    "execution_note": "✅ REAL TRADE PLACED via Alpaca API",
                    "alpaca_response": "Order submitted successfully"
                }
            }
        else:
            # API error - return simulation
            raise Exception(f"Alpaca API error: {response.status_code} - {response.text}")
            
    except Exception as e:
        # Fallback to simulation on any error
        order_id = f"ERR_{strategy.upper()}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        return {
            "order": {
                "id": order_id,
                "symbol": symbol,
                "side": side,
                "qty": int(qty),
                "type": "market",
                "entry_price": entry_price,
                "stop_loss": stop_loss,
                "take_profit": take_profit,
                "status": "simulation_due_to_error"
            },
            "algorithm": strategy,
            "strategy_details": {
                **strategy_details,
                "risk_reward": round(abs(take_profit - entry_price) / abs(entry_price - stop_loss), 2),
                "position_size_logic": f"{int(qty)} units based on 2% account risk",
                "execution_time": datetime.now().isoformat(),
                "execution_note": f"⚠️ API Error: {str(e)[:100]}",
                "fallback_mode": "Simulation used due to API error"
            }
        }


def execute_options_strategy(
    symbol: str,
    strategy_type: str,
    legs: List[Dict],
    strategy_details: Dict
) -> Dict:
    """Execute options strategy with multiple legs."""
    
    order_id = f"OPTIONS_{strategy_type.upper()}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    return {
        "order": {
            "id": order_id,
            "symbol": symbol,
            "strategy_type": strategy_type,
            "legs": legs,
            "status": "pending_new"
        },
        "algorithm": f"Options {strategy_type}",
        "strategy_details": {
            **strategy_details,
            "execution_time": datetime.now().isoformat(),
            "options_specific": True
        }
    }


# =============================================================================
# MASTER SCANNER FUNCTION
# =============================================================================

def scan_all_strategies(symbols: List[str], asset_classes: List[str] = None) -> Dict[str, List[Dict]]:
    """Master function to scan all strategies across asset classes with parallel processing."""
    
    if asset_classes is None:
        asset_classes = ["stocks", "crypto", "options", "fundamentals"]
    
    all_signals = {}
    market_status = {
        "stocks_open": is_stock_market_open(),
        "extended_hours": is_extended_hours(),
        "crypto_open": True  # Always open
    }
    
    logger.info(f"Market status: Stocks={'Open' if market_status['stocks_open'] else 'Closed'}, "
                f"Extended Hours={'Yes' if market_status['extended_hours'] else 'No'}")
    
    # Use ThreadPoolExecutor for parallel scanning
    with ThreadPoolExecutor(max_workers=10) as executor:
        futures = {}
        
        # Only scan stocks if market is open or in extended hours
        if "stocks" in asset_classes and (market_status["stocks_open"] or market_status["extended_hours"]):
            # Submit stock strategy scans in parallel
            futures["ma_crossover"] = executor.submit(scan_ma_crossover, symbols)
            futures["bollinger_reversion"] = executor.submit(scan_bollinger_reversion, symbols)
            futures["donchian_breakout"] = executor.submit(scan_donchian_breakout, symbols)
            futures["rsi_momentum"] = executor.submit(scan_rsi_momentum, symbols)
            futures["volume_spike"] = executor.submit(scan_volume_spike, symbols)
            futures["ttm_squeeze"] = executor.submit(scan_ttm_squeeze, symbols[:100])  # Limit for performance
        elif "stocks" in asset_classes:
            logger.info("Stock market closed - skipping stock scanners")
            all_signals["market_closed_stocks"] = [{
                "message": "US stock market is closed. Trading hours are 9:30 AM - 4:00 PM ET on weekdays."
            }]
        
        # Crypto runs 24/7
        if "crypto" in asset_classes:
            crypto_symbols = [s for s in symbols if any(c in s for c in ['BTC', 'ETH', 'USD'])]
            if crypto_symbols:
                futures["crypto_macd"] = executor.submit(scan_crypto_macd, crypto_symbols)
                futures["crypto_rsi"] = executor.submit(scan_crypto_rsi_oversold, crypto_symbols)
                futures["crypto_onchain"] = executor.submit(scan_crypto_onchain_nvt, crypto_symbols)
        
        # Options only during market hours - comprehensive scanning
        if "options" in asset_classes and market_status["stocks_open"]:
            # Premium selling strategies
            futures["iron_condor"] = executor.submit(scan_iron_condor_setups, symbols[:50])
            futures["iron_butterfly"] = executor.submit(scan_iron_butterfly_setups, symbols[:50])
            futures["covered_call"] = executor.submit(scan_covered_call_setups, symbols[:50])
            futures["cash_secured_put"] = executor.submit(scan_cash_secured_put_setups, symbols[:50])
            
            # Directional strategies
            futures["vertical_spread"] = executor.submit(scan_vertical_spread_setups, symbols[:50])
            futures["diagonal_spread"] = executor.submit(scan_diagonal_spread_setups, symbols[:50])
            
            # Volatility strategies
            futures["long_straddle"] = executor.submit(scan_long_straddle_setups, symbols[:50])
            futures["calendar_spread"] = executor.submit(scan_calendar_spread_setups, symbols[:50])
            futures["ratio_spread"] = executor.submit(scan_ratio_spread_setups, symbols[:30])
        elif "options" in asset_classes:
            logger.info("Options market closed - skipping options scanners")
        
        # Fundamentals can run anytime (data doesn't change intraday)
        if "fundamentals" in asset_classes:
            stock_symbols = [s for s in symbols if not any(c in s for c in ['BTC', 'ETH', 'USD'])]
            if stock_symbols:
                futures["insider_buying"] = executor.submit(scan_insider_buying, stock_symbols)
                futures["analyst_upgrades"] = executor.submit(scan_analyst_upgrades, stock_symbols)
        
        # Collect results as they complete
        for strategy_name, future in futures.items():
            try:
                result = future.result(timeout=30)  # 30 second timeout per strategy
                all_signals[strategy_name] = result
                logger.info(f"Completed {strategy_name}: {len(result)} signals")
            except Exception as e:
                logger.error(f"Error in {strategy_name}: {str(e)}")
                all_signals[strategy_name] = []
    
    # If no signals found, try relaxed criteria
    total_signals = sum(len(signals) for signals in all_signals.values() if isinstance(signals, list))
    
    if total_signals == 0:
        logger.info("No signals found with primary criteria, trying relaxed thresholds...")
        
        with ThreadPoolExecutor(max_workers=5) as executor:
            fallback_futures = {}
            
            # Relaxed stock scans (if market open)
            if market_status["stocks_open"] or market_status["extended_hours"]:
                # Try shorter EMA periods
                fallback_futures["ma_crossover_relaxed"] = executor.submit(
                    scan_ma_crossover, symbols[:50], 5, 13  # Faster crossover
                )
                # Lower RSI thresholds
                fallback_futures["rsi_momentum_relaxed"] = executor.submit(
                    scan_rsi_momentum, symbols[:50], 35, 50, 65  # Less extreme levels
                )
            
            # Always try crypto with relaxed settings
            if crypto_symbols:
                # Try shorter timeframe MACD
                fallback_futures["crypto_macd_15min"] = executor.submit(
                    scan_crypto_macd, crypto_symbols, 6, 13, 5  # Faster MACD
                )
            
            # Collect fallback results
            for strategy_name, future in fallback_futures.items():
                try:
                    result = future.result(timeout=15)
                    if result:
                        all_signals[strategy_name] = result
                        logger.info(f"Fallback {strategy_name}: {len(result)} signals")
                except Exception as e:
                    logger.error(f"Fallback error in {strategy_name}: {str(e)}")
    
    return all_signals 

def generate_basket_plan(goal: float, capital: float, signals: List[Dict], basket_size: int = 5) -> List[Dict]:
    """
    Builds a basket of up to `basket_size` trades, each risking ≤(goal/basket_size)
    and targeting exactly (goal/basket_size) profit.
    
    Args:
        goal: Total profit target
        capital: Available capital
        signals: List of trading signals with scores
        basket_size: Number of trades in the basket
        
    Returns:
        List of trade plans with position sizing
    """
    per_trade_goal = goal / basket_size  # e.g. $5/5 = $1 each
    per_trade_risk = per_trade_goal  # Default 1:1 risk/reward for basket trades
    
    # Sort signals by score descending
    ranked = sorted(signals, key=lambda s: s.get("score", 0), reverse=True)
    plan = []
    
    # Build each micro-trade
    for sig in ranked[:basket_size * 2]:  # Look at 2x candidates
        if len(plan) >= basket_size:
            break
            
        exp = sig.get("expected_return", 0)  # $ per unit
        risk = sig.get("risk", 0)  # $ loss per unit
        
        if exp <= 0 or risk <= 0:
            continue
            
        # Calculate position size
        # Minimum units to hit profit goal
        qty_target = math.ceil(per_trade_goal / exp)
        # Max units so you never risk > per_trade_risk
        max_by_risk = math.floor(per_trade_risk / risk)
        qty = max(0, min(qty_target, max_by_risk))
        
        if qty == 0:
            continue  # skip unplayable signals
            
        # For crypto, allow fractional units
        is_crypto = "USD" in sig.get("symbol", "") and any(crypto in sig.get("symbol", "") for crypto in ["BTC", "ETH", "XRP", "LTC", "SOL", "DOGE"])
        if is_crypto:
            qty = round(qty, 4)
        else:
            qty = int(qty)
            
        if qty == 0:
            continue
            
        plan.append({
            "symbol": sig["symbol"],
            "strategy": sig.get("algorithm", sig.get("strategy", "Unknown")),
            "signal_type": sig.get("signal_type", "BUY"),
            "qty": qty,
            "entry": sig["entry_price"],
            "tp": sig["entry_price"] + exp,  # take-profit price
            "sl": sig["entry_price"] - risk,  # stop-loss price
            "risk": risk * qty,
            "profit": exp * qty,
            "confidence": sig.get("confidence", "Medium"),
            "market": "crypto" if is_crypto else "stocks"
        })
    
    return plan

def scan_all_markets(constraints: Dict = None) -> List[Dict]:
    """
    Scan all available markets based on constraints.
    
    Args:
        constraints: Dict with markets, max_loss, horizon, etc.
        
    Returns:
        List of all signals from all markets
    """
    if constraints is None:
        constraints = {}
        
    allowed_markets = constraints.get("markets", ["stocks", "crypto", "options"])
    all_signals = []
    
    # Import here to avoid circular imports
    from engine import call_strategy_endpoint, enhance_signals, is_stock_market_open
    
    # Crypto scans (24/7)
    if "crypto" in allowed_markets:
        # MACD scan
        crypto_macd = call_strategy_endpoint("scan_crypto_macd", {})
        if crypto_macd:
            enhanced = enhance_signals(crypto_macd, "Crypto MACD")
            all_signals.extend(enhanced)
            
        # RSI scan
        crypto_rsi = call_strategy_endpoint("scan_crypto_rsi", {})
        if crypto_rsi:
            enhanced = enhance_signals(crypto_rsi, "Crypto RSI")
            all_signals.extend(enhanced)
    
    # Stock scans (market hours only)
    if "stocks" in allowed_markets and is_stock_market_open():
        # Master scanner
        master_scan = call_strategy_endpoint("scan_all_strategies", {
            "asset_classes": ["stocks", "options", "fundamentals"]
        })
        if master_scan:
            enhanced = enhance_signals(master_scan, "Master Scanner")
            all_signals.extend(enhanced)
            
        # EMA crossover
        ema_scan = call_strategy_endpoint("scan_ma_crossover", {})
        if ema_scan:
            enhanced = enhance_signals(ema_scan, "EMA Crossover")
            all_signals.extend(enhanced)
            
        # Bollinger reversion
        bb_scan = call_strategy_endpoint("scan_bollinger_reversion", {})
        if bb_scan:
            enhanced = enhance_signals(bb_scan, "Bollinger Reversion")
            all_signals.extend(enhanced)
    
    # Options scans
    if "options" in allowed_markets and is_stock_market_open():
        # Long straddle
        straddle_scan = call_strategy_endpoint("scan_long_straddle", {})
        if straddle_scan:
            enhanced = enhance_signals(straddle_scan, "Long Straddle")
            all_signals.extend(enhanced)
    
    return all_signals 

def detect_ttm_squeeze_signals(df):
    """
    Detect TTM Squeeze signals and score them according to the enhanced 6-point system.
    Returns a DataFrame with squeeze status, histogram, score, and first yellow bar flag.
    """
    df = df.copy()
    # Bollinger Bands (20, 2)
    df['bb_mid'] = df['close'].rolling(20).mean()
    df['bb_std'] = df['close'].rolling(20).std()
    df['bb_upper'] = df['bb_mid'] + 2 * df['bb_std']
    df['bb_lower'] = df['bb_mid'] - 2 * df['bb_std']
    # Keltner Channels (20 EMA, 1.5 ATR)
    df['ema20'] = df['close'].ewm(span=20, adjust=False).mean()
    df['tr'] = np.maximum(df['high'] - df['low'], np.maximum(abs(df['high'] - df['close'].shift()), abs(df['low'] - df['close'].shift())))
    df['atr'] = df['tr'].rolling(20).mean()
    df['kc_upper'] = df['ema20'] + 1.5 * df['atr']
    df['kc_lower'] = df['ema20'] - 1.5 * df['atr']
    # Squeeze On: BB inside KC
    df['squeeze_on'] = (df['bb_lower'] > df['kc_lower']) & (df['bb_upper'] < df['kc_upper'])
    # 4-bar momentum histogram
    df['hist'] = df['close'] - df['close'].shift(4)
    # EMA alignment
    df['ema8'] = df['close'].ewm(span=8, adjust=False).mean()
    df['ema21'] = df['close'].ewm(span=21, adjust=False).mean()
    df['ema8_rising'] = df['ema8'] > df['ema8'].shift(1)
    df['ema21_rising'] = df['ema21'] > df['ema21'].shift(1)
    # EMA5
    df['ema5'] = df['close'].ewm(span=5, adjust=False).mean()
    # Volume spike
    df['vol_avg'] = df['volume'].rolling(20).mean()
    df['vol_spike'] = df['volume'] > 1.5 * df['vol_avg']
    # First yellow bar logic
    df['first_yellow_bar'] = False
    neg_count = 0
    for i in range(5, len(df)):
        # Count consecutive negative histogram bars
        if df['hist'].iloc[i-1] < 0:
            neg_count += 1
        else:
            neg_count = 0
        # First yellow bar: 4+ negative, current still negative but higher than previous
        if (
            neg_count >= 4 and
            df['hist'].iloc[i] < 0 and
            df['hist'].iloc[i] > df['hist'].iloc[i-1]
        ):
            df.at[df.index[i], 'first_yellow_bar'] = True
        # Reset if not negative
        if df['hist'].iloc[i-1] >= 0:
            neg_count = 0
    # Scoring system
    df['score'] = 0.0
    for i in range(5, len(df)):
        score = 0.0
        # Squeeze On
        if df['squeeze_on'].iloc[i]:
            score += 2
        # Histogram pattern
        if df['first_yellow_bar'].iloc[i]:
            score += 2
        # EMA alignment
        if df['ema8'].iloc[i] > df['ema21'].iloc[i] and df['ema8_rising'].iloc[i] and df['ema21_rising'].iloc[i]:
            score += 2
        # Price momentum
        if df['close'].iloc[i] > df['close'].iloc[i-1]:
            score += 2
        # Volume spike
        if df['vol_spike'].iloc[i]:
            score += 1.5
        # Above EMA5
        if df['close'].iloc[i] > df['ema5'].iloc[i]:
            score += 0.5
        df.at[df.index[i], 'score'] = score
    # High quality signal flag
    df['high_quality_signal'] = (df['score'] >= 7) & (df['first_yellow_bar'])
    return df[['close','squeeze_on','hist','first_yellow_bar','score','high_quality_signal']]

def linear_regression_histogram(closes, lookback=20):
    hist = [np.nan] * lookback
    for i in range(lookback, len(closes)):
        slice_ = closes[i - lookback + 1:i + 1]
        n = len(slice_)
        x = np.arange(n)
        y = np.array(slice_)
        x_sum = x.sum()
        y_sum = y.sum()
        xy_sum = (x * y).sum()
        xx_sum = (x * x).sum()
        slope = (n * xy_sum - x_sum * y_sum) / (n * xx_sum - x_sum * x_sum)
        intercept = (y_sum - slope * x_sum) / n
        linreg_value = intercept + slope * (n - 1)
        recent_prices = closes[max(0, i - 10):i + 1]
        price_range = np.max(recent_prices) - np.min(recent_prices)
        normalization = price_range if price_range != 0 else 1
        momentum = (closes[i] - linreg_value) / normalization
        hist.append(momentum)
    return np.array(hist)

def detect_ttm_squeeze(data: pd.DataFrame) -> List[Dict]:
    if len(data) < 50:
        return []
    closes = data['close'].values
    volumes = data['volume'].values
    # Bollinger Bands (20, 2)
    bb_mid = pd.Series(closes).rolling(20).mean()
    bb_std = pd.Series(closes).rolling(20).std()
    bb_upper = bb_mid + 2 * bb_std
    bb_lower = bb_mid - 2 * bb_std
    # Keltner Channels (20 EMA, 1.5 ATR)
    ema20 = pd.Series(closes).ewm(span=20, adjust=False).mean()
    tr = np.maximum(data['high'] - data['low'], np.maximum(abs(data['high'] - data['close'].shift()), abs(data['low'] - data['close'].shift())))
    atr = pd.Series(tr).rolling(20).mean()
    kc_upper = ema20 + 1.5 * atr
    kc_lower = ema20 - 1.5 * atr
    # EMAs
    ema8 = pd.Series(closes).ewm(span=8, adjust=False).mean()
    ema21 = pd.Series(closes).ewm(span=21, adjust=False).mean()
    ema5 = pd.Series(closes).ewm(span=5, adjust=False).mean()
    # Histogram (linear regression based)
    hist = linear_regression_histogram(closes, lookback=20)
    # Volume average
    vol_avg = pd.Series(volumes).rolling(20).mean()
    results = []
    for i in range(50, len(data)):
        squeeze_on = (bb_lower.iloc[i] > kc_lower.iloc[i]) and (bb_upper.iloc[i] < kc_upper.iloc[i])
        # Histogram pattern: 4+ negative, then improvement
        neg_count = 0
        for j in range(i-4, i):
            if hist[j] < 0:
                neg_count += 1
        hist_pattern = neg_count >= 4 and hist[i] < 0 and hist[i] > hist[i-1]
        # EMA trend
        ema_rising = ema8.iloc[i] > ema8.iloc[i-1] and ema21.iloc[i] > ema21.iloc[i-1]
        ema_cross = ema8.iloc[i] > ema21.iloc[i]
        # Price momentum
        momentum_up = closes[i] > closes[i-1]
        # Volume spike
        vol_spike = volumes[i] > vol_avg.iloc[i] * 1.5
        exceptional_vol = volumes[i] > vol_avg.iloc[i] * 2.0
        # Above EMA5
        above_ema5 = closes[i] > ema5.iloc[i]
        # Extended pattern
        neg_count_ext = 0
        for j in range(i-5, i):
            if hist[j] < 0:
                neg_count_ext += 1
        extended_pattern = neg_count_ext >= 5
        # Scoring
        score = 0.0
        if squeeze_on:
            score += 2.0
        if hist_pattern:
            score += 2.0
        if ema_rising and ema_cross:
            score += 2.0
        if momentum_up:
            score += 2.0
        if vol_spike:
            score += 1.5
        if above_ema5:
            score += 0.5
        if exceptional_vol:
            score += 0.5
        if extended_pattern:
            score += 0.5
        # Yellow bar detection (30% improvement)
        is_yellow = False
        if hist[i-1] < 0 and hist[i] < 0 and hist[i] > hist[i-1] and abs(hist[i] - hist[i-1]) > 0.1 and hist[i] > hist[i-1] * 0.7:
            is_yellow = True
        # Red-to-green transition
        first_positive_bar = hist[i-1] < 0 and hist[i] > 0
        # Signal
        signal = squeeze_on and hist_pattern and ema_rising and ema_cross and momentum_up and vol_spike
        results.append({
            **{k: data[k].iloc[i] for k in data.columns if k in ['time','close','high','low','open','volume']},
            'bb_upper': bb_upper.iloc[i],
            'bb_lower': bb_lower.iloc[i],
            'kc_upper': kc_upper.iloc[i],
            'kc_lower': kc_lower.iloc[i],
            'squeeze_on': squeeze_on,
            'hist': hist[i],
            'ema8': ema8.iloc[i],
            'ema21': ema21.iloc[i],
            'signal': signal,
            'score': min(score, 10),
            'is_yellow': is_yellow,
            'first_positive_bar': first_positive_bar
        })
    return results
