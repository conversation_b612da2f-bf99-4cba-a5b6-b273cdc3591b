"""
ATLAS Autonomous Trading Engine
Orchestrates all strategies, handles errors, and executes optimal trades automatically
"""

import traceback
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import requests
import os
import math
import pytz

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def log_error(message: str):
    """Enhanced error logging with JSON structure."""
    logger.error(f"[ATLAS_ERROR] {message}")

def log_info(message: str):
    """Info logging for orchestrator actions."""
    logger.info(f"[ATLAS_ENGINE] {message}")

def is_stock_market_open():
    """Check if US stock market is currently open."""
    # Get New York time now
    now_et = datetime.now(pytz.timezone("US/Eastern"))
    # Weekend? 5=Saturday, 6=Sunday
    if now_et.weekday() >= 5:
        return False
    # Build today's open/close timestamps
    open_dt = now_et.replace(hour=9, minute=30, second=0, microsecond=0)
    close_dt = now_et.replace(hour=16, minute=0, second=0, microsecond=0)
    # Return True if between 9:30–16:00
    return open_dt <= now_et <= close_dt

def call_strategy_endpoint(endpoint: str, payload: Dict = None) -> List[Dict]:
    """
    Call a strategy endpoint with error handling.
    Returns empty list on error instead of crashing.
    """
    try:
        # HARDCODE port 8080 to avoid environment variable issues
        proxy_url = "http://127.0.0.1:8080"
        log_info(f"Calling endpoint: {endpoint} at {proxy_url}")
        url = f"{proxy_url}/{endpoint}"
        
        if payload:
            response = requests.post(url, json=payload, timeout=45)
        else:
            response = requests.post(url, json={}, timeout=45)
        
        if response.status_code == 200:
            result = response.json()
            # Handle different response formats
            if isinstance(result, list):
                return result
            elif isinstance(result, dict):
                # Flatten any list values in the dictionary
                aggregated: List = []
                for v in result.values():
                    if isinstance(v, list):
                        aggregated.extend(v)
                if aggregated:
                    return aggregated
                # Fallback to legacy single-key formats
                for key in ['signals', 'ma_crossover', 'bollinger_reversion', 'crypto_macd', 'long_straddle', 'insider_buying']:
                    if key in result:
                        return result[key] if isinstance(result[key], list) else []
                return [result] if result else []
            return []
        else:
            log_error(f"HTTP {response.status_code} from {endpoint}: {response.text}")
            return []
            
    except Exception as e:
        log_error(f"Error calling {endpoint}: {str(e)}\n{traceback.format_exc()}")
        return []

def enhance_signals(signals: List[Dict], strategy_name: str) -> List[Dict]:
    """
    Enhance signals with standardized risk/reward metrics and strategy info.
    """
    enhanced = []
    
    for signal in signals:
        try:
            # Standardize signal format
            enhanced_signal = {
                "symbol": signal.get("symbol", "UNKNOWN"),
                "strategy": strategy_name,
                "algorithm": signal.get("algorithm", strategy_name),
                "signal_type": signal.get("signal", "BUY"),
                "entry_price": float(signal.get("entry_price", 100)),
                "confidence": signal.get("confidence", "Medium"),
                "timeframe": signal.get("timeframe", "1D")
            }
            
            # Calculate risk and expected return
            entry_price = enhanced_signal["entry_price"]
            
            # Estimate stop loss and take profit based on strategy
            if "crypto" in strategy_name.lower():
                stop_loss_pct = 0.03    # 3% stop for crypto
                take_profit_pct = 0.06  # 6% target for crypto
            elif "options" in strategy_name.lower():
                stop_loss_pct = 0.50    # 50% stop for options
                take_profit_pct = 1.00  # 100% target for options
            else:
                stop_loss_pct = 0.02    # 2% stop for stocks
                take_profit_pct = 0.04  # 4% target for stocks
            
            # Calculate dollar amounts
            enhanced_signal["risk"] = entry_price * stop_loss_pct
            enhanced_signal["expected_return"] = entry_price * take_profit_pct
            enhanced_signal["risk_reward_ratio"] = take_profit_pct / stop_loss_pct
            
            # Confidence scoring
            confidence_multiplier = {
                "High": 1.0,
                "Medium": 0.7,
                "Low": 0.4
            }.get(enhanced_signal["confidence"], 0.5)
            
            # Calculate composite score
            enhanced_signal["score"] = (
                enhanced_signal["expected_return"] / (enhanced_signal["risk"] + 0.01) 
                * confidence_multiplier
            )
            
            enhanced.append(enhanced_signal)
            
        except Exception as e:
            log_error(f"Error enhancing signal {signal}: {str(e)}")
            continue
    
    return enhanced

def orchestrate_opportunity(goal: float, capital: float, constraints: Dict = None, prior_suggestion: Dict = None) -> Dict[str, Any]:
    """
    Master orchestrator: scan all markets, pick best trade, execute automatically.
    
    Args:
        goal: Dollar profit target
        capital: Available trading capital
        constraints: User preferences (markets, max_loss, etc.)
        prior_suggestion: Previous suggestion if user declined
        
    Returns:
        Dict with signal, execution result, or clarification needs
    """
    log_info(f"Starting orchestration: Goal=${goal}, Capital=${capital:,.0f}")
    
    # 1) If prior suggestion was presented & user said "no", we must clarify:
    if prior_suggestion and prior_suggestion.get("requires_confirmation"):
        return {
            "needs_clarification": True,
            "questions": [
                "Would you like to lower your max risk per trade?",
                "Switch asset classes (stocks vs crypto vs options)?",
                "Adjust your profit target or time horizon?",
                "Any specific assets you'd prefer to avoid?",
                "Would you prefer a diversified basket of smaller trades?"
            ],
            "context": "I understand you declined the previous suggestion. Let me find a better opportunity for you."
        }
    
    # 2) If we lack any constraint, ask for it:
    missing = []
    if constraints is None:
        constraints = {}
    
    if "markets" not in constraints:
        missing.append("Which markets should I scan? (stocks, crypto, options, or all)")
    if "max_loss" not in constraints:
        missing.append("What's your maximum acceptable loss per trade?")
    if "avoid_symbols" not in constraints and prior_suggestion:
        missing.append("Any specific symbols you'd like me to avoid?")
    
    if missing:
        return {
            "needs_clarification": True,
            "questions": missing,
            "context": "I need a bit more information to find the perfect trade for you:"
        }
    
    # Extract constraints
    allowed_markets = constraints.get("markets", ["stocks", "crypto", "options"])
    max_loss = constraints.get("max_loss", goal)  # Default to goal amount
    avoid_symbols = constraints.get("avoid_symbols", [])
    basket_mode = constraints.get("basket_mode", False)  # New: basket trading mode
    basket_size = constraints.get("basket_size", 5)  # Number of trades in basket
    
    # Check market status
    market_status = {
        "stocks_open": is_stock_market_open(),
        "extended_hours": False,  # Simplified for now
        "crypto_open": True,
        "current_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S %Z")
    }
    
    # Create market status message
    market_message = "US stock market is closed. Focusing on crypto and fundamentals." if not market_status["stocks_open"] else "US stock market is open. Scanning all markets."
    
    log_info(f"Market status: {market_message}")
    
    # Import here to avoid circular dependency
    from strategies import scan_all_markets, generate_basket_plan
    
    # Get all signals from all markets
    all_signals = scan_all_markets(constraints)
    
    # Filter out avoided symbols
    if avoid_symbols:
        all_signals = [s for s in all_signals if s.get("symbol") not in avoid_symbols]
    
    # If no signals found, ask for different constraints
    if not all_signals:
        log_info("No signals found.")
        
        return {
            "needs_clarification": True,
            "questions": [
                "Would you like me to scan different markets?",
                "Should I look for longer-term opportunities (daily/weekly)?",
                "Would you consider a smaller profit target to find more opportunities?",
                "Should I include more volatile assets?",
                "Would you like to try a diversified basket approach?"
            ],
            "context": f"I couldn't find any signals matching your criteria (Goal: ${goal}, Max Loss: ${max_loss}, Markets: {', '.join(allowed_markets)}). Let's adjust the parameters:",
            "no_signals": True
        }
    
    # BASKET MODE: Generate a diversified basket of trades
    if basket_mode or len(all_signals) >= basket_size * 2:
        log_info(f"Generating basket plan with {basket_size} trades")
        
        basket_plan = generate_basket_plan(goal, capital, all_signals, basket_size)
        
        if len(basket_plan) < basket_size:
            # Not enough viable trades for a full basket
            return {
                "needs_clarification": True,
                "questions": [
                    f"I could only find {len(basket_plan)} viable trades for your basket. Would you like to:",
                    "• Proceed with a smaller basket?",
                    "• Lower your per-trade profit target?",
                    "• Include additional markets?",
                    "• Switch to a single larger trade?"
                ],
                "context": f"Basket generation issue:",
                "partial_basket": basket_plan
            }
        
        # Calculate totals
        total_risk = sum(t["risk"] for t in basket_plan)
        total_profit = sum(t["profit"] for t in basket_plan)
        total_capital = sum(t["qty"] * t["entry"] for t in basket_plan)
        
        # Create basket summary
        basket_summary = f"Found {len(basket_plan)} trades across {len(set(t['market'] for t in basket_plan))} markets. "
        basket_summary += f"Total investment: ${total_capital:,.2f}, "
        basket_summary += f"Total risk: ${total_risk:.2f}, "
        basket_summary += f"Total profit target: ${total_profit:.2f}"
        
        return {
            "status": "basket_proposal",
            "requires_confirmation": True,
            "basket_mode": True,
            "goal": goal,
            "market_status": market_status,
            "basket_plan": basket_plan,
            "basket_summary": basket_summary,
            "total_risk": total_risk,
            "total_profit": total_profit,
            "total_capital_required": total_capital,
            "confirmation_message": f"Ready to execute {len(basket_plan)} trades? This will invest ${total_capital:,.2f} total with maximum combined risk of ${total_risk:.2f}.",
            "position_sizing_explanation": {
                "plain_english": f"I've created a diversified basket of {len(basket_plan)} micro-trades. Each trade risks approximately ${total_risk/len(basket_plan):.2f} and targets ${total_profit/len(basket_plan):.2f} profit. Together they aim for your ${goal} goal while spreading risk across multiple assets.",
                "basket_details": basket_plan
            }
        }
    
    # SINGLE TRADE MODE (existing logic)
    # Rank signals by score (risk-adjusted return * confidence)
    all_signals.sort(key=lambda x: x["score"], reverse=True)
    
    # Filter by max loss constraint
    valid_signals = []
    for signal in all_signals:
        position_size = calculate_position_size(goal, signal, capital, max_loss)
        if position_size > 0:
            signal["calculated_position_size"] = position_size
            valid_signals.append(signal)
    
    if not valid_signals:
        # Suggest basket mode if single trades don't work
        return {
            "needs_clarification": True,
            "questions": [
                f"No single trade fits your ${max_loss} risk limit. Would you like to:",
                "• Try a diversified basket of smaller trades?",
                "• Accept a slightly higher risk?",
                "• Look for less volatile assets?",
                "• Wait for better market conditions?"
            ],
            "context": f"Found {len(all_signals)} signals, but none fit within your risk limit.",
            "suggest_basket": True
        }
    
    # Pick the best valid signal
    best_signal = valid_signals[0]
    position_size = best_signal["calculated_position_size"]
    
    log_info(f"Best signal: {best_signal['algorithm']} on {best_signal['symbol']} (Score: {best_signal['score']:.3f})")
    
    # Calculate actual values
    capital_required = position_size * best_signal["entry_price"]
    actual_profit = position_size * best_signal["expected_return"]
    actual_risk = position_size * best_signal["risk"]
    
    # Create detailed explanation
    position_explanation = create_position_explanation(
        goal, best_signal, position_size, capital, capital_required, actual_profit, actual_risk
    )
    
    # Return trade proposal
    log_info(f"Trade proposal ready: {position_size} units of {best_signal['symbol']}")
    
    return {
        "status": "proposal",
        "requires_confirmation": True,
        "goal": goal,
        "capital_used": capital_required,
        "market_status": market_status,
        "scanner_results": {"signals_found": len(all_signals), "valid_signals": len(valid_signals)},
        "total_signals_found": len(all_signals),
        "valid_signals_count": len(valid_signals),
        "constraints_applied": constraints,
        "best_signal": best_signal,
        "proposed_trade": {
            "symbol": best_signal["symbol"],
            "strategy": best_signal["algorithm"],
            "signal": best_signal["signal_type"],
            "qty": position_size,
            "entry_price": best_signal["entry_price"],
            "strategy_details": best_signal
        },
        "expected_profit": actual_profit,
        "max_risk": actual_risk,
        "confidence": best_signal["confidence"],
        "position_sizing_explanation": position_explanation,
        "confidence_explanation": create_confidence_explanation(best_signal),
        "plan_summary": f"{market_message} "
                      f"Found {len(all_signals)} signals ({len(valid_signals)} within risk limit). "
                      f"Best: {best_signal['algorithm']} on {best_signal['symbol']}. "
                      f"Proposing to buy {position_size} units at ${best_signal['entry_price']:.2f}. "
                      f"Target profit: ${actual_profit:.2f} (goal was ${goal}), "
                      f"Max risk: ${actual_risk:.2f} (limit was ${max_loss})",
        "confirmation_message": f"Ready to execute: Buy {position_size} units of {best_signal['symbol']} at ${best_signal['entry_price']:,.2f}? This will invest ${capital_required:,.2f} with a maximum risk of ${actual_risk:.2f}.",
        "suggest_basket": f"💡 Tip: Consider using basket mode for better diversification across {min(5, len(all_signals))} trades"
    }

def calculate_position_size(goal: float, signal: Dict, capital: float, max_loss: float) -> float:
    """Calculate position size based on goal and risk constraints."""
    try:
        expected_return_per_share = signal["expected_return"]
        risk_per_share = signal["risk"]
        entry_price = signal["entry_price"]
        
        # Calculate shares needed to hit goal
        qty_for_goal = goal / expected_return_per_share if expected_return_per_share > 0 else 0
        
        # But don't risk more than max_loss
        max_qty_by_risk = max_loss / risk_per_share if risk_per_share > 0 else 0
        
        # Take the smaller to ensure we don't exceed risk limit
        position_size = min(qty_for_goal, max_qty_by_risk) if qty_for_goal > 0 and max_qty_by_risk > 0 else 0
        
        # Also check capital constraints
        max_qty_by_capital = capital * 0.1 / entry_price  # Max 10% of capital
        position_size = min(position_size, max_qty_by_capital) if position_size > 0 else 0
        
        # For crypto, adjust for fractional shares
        is_crypto = "USD" in signal.get("symbol", "") and any(crypto in signal.get("symbol", "") for crypto in ["BTC", "ETH"])
        if is_crypto and entry_price > 1000 and position_size > 0:
            position_size = round(position_size, 4)  # 4 decimal places for crypto
        elif position_size > 0:
            position_size = max(1, int(position_size))  # Whole shares for stocks
            
        return position_size
        
    except Exception as e:
        log_error(f"Position sizing error: {str(e)}")
        return 0

def create_position_explanation(goal: float, signal: Dict, position_size: float, 
                               capital: float, capital_required: float, 
                               actual_profit: float, actual_risk: float) -> Dict:
    """Create detailed position sizing explanation."""
    is_crypto = "USD" in signal.get("symbol", "") and any(crypto in signal.get("symbol", "") for crypto in ["BTC", "ETH"])
    
    return {
        "calculation_steps": {
            "step1_goal_based": f"To make ${goal:.2f} profit: {goal:.2f} ÷ ${signal['expected_return']:.2f} per unit = {goal/signal['expected_return']:.4f} units needed",
            "step2_risk_limit": f"Risk constraint: {actual_risk:.2f} ÷ ${signal['risk']:.2f} risk per unit = {position_size:.4f} units max",
            "step3_capital_limit": f"With 10% of ${capital:,.0f} capital: {capital * 0.1:,.2f} ÷ ${signal['entry_price']:,.2f} = {(capital * 0.1)/signal['entry_price']:.4f} units max",
            "step4_final": f"Taking minimum of all constraints = {position_size} {'units' if is_crypto else 'shares'}"
        },
        "plain_english": f"To target ${goal:.2f} with this {signal['symbol']} signal, you'd buy {'about' if is_crypto else ''} {position_size} {'BTC' if 'BTC' in signal['symbol'] else 'ETH' if 'ETH' in signal['symbol'] else 'shares'}, which costs roughly ${capital_required:,.2f} up-front. Your maximum loss (at the stop-loss level) would be about ${actual_risk:.2f}, giving you a {signal['risk_reward_ratio']:.1f}:1 risk/reward ratio.",
        "capital_breakdown": {
            "total_investment": f"${capital_required:,.2f}",
            "percentage_of_capital": f"{(capital_required/capital)*100:.1f}%",
            "cash_remaining": f"${capital - capital_required:,.2f}"
        },
        "risk_reward_breakdown": {
            "risk_amount": f"${actual_risk:.2f}",
            "reward_amount": f"${actual_profit:.2f}",
            "ratio": f"{signal['risk_reward_ratio']:.1f}:1",
            "risk_type": "conservative" if signal['risk_reward_ratio'] >= 2 else "moderate" if signal['risk_reward_ratio'] >= 1.5 else "aggressive"
        }
    }

def create_confidence_explanation(signal: Dict) -> Dict:
    """Create confidence explanation based on signal strength."""
    return {
        "level": signal["confidence"],
        "reason": "High confidence - strong technical signal with good volume" if signal["confidence"] == "High" else 
                "Medium confidence - decent signal but needs confirmation" if signal["confidence"] == "Medium" else
                "Low confidence - weak signal, consider waiting for better setup",
        "how_to_improve": [
            "Wait for stronger technical confirmations",
            "Look for signals with higher volume",
            "Consider multiple timeframe alignment",
            "Check for fundamental catalysts"
        ]
    }

def execute_proposed_trade(trade_proposal: Dict[str, Any]) -> Dict[str, Any]:
    """
    Execute a previously proposed trade after user confirmation.
    
    Args:
        trade_proposal: The proposed trade details from orchestrate_opportunity
        
    Returns:
        Dict with execution result
    """
    try:
        if "proposed_trade" not in trade_proposal:
            return {
                "status": "error",
                "message": "Invalid trade proposal - missing trade details"
            }
        
        trade = trade_proposal["proposed_trade"]
        
        log_info(f"Executing confirmed trade: {trade['qty']} units of {trade['symbol']}")
        
        execution_result = execute_best_trade(
            symbol=trade["symbol"],
            strategy=trade["strategy"],
            signal=trade["signal"],
            qty=trade["qty"],
            entry_price=trade["entry_price"],
            strategy_details=trade["strategy_details"]
        )
        
        return {
            "status": "success",
            "execution": execution_result,
            "message": f"Trade executed successfully! Order ID: {execution_result.get('order', {}).get('id', 'Unknown')}",
            "summary": f"Bought {trade['qty']} {'units' if trade['qty'] < 1 else 'shares'} of {trade['symbol']} at ${trade['entry_price']:,.2f}"
        }
        
    except Exception as e:
        log_error(f"Trade execution error: {str(e)}")
        return {
            "status": "error",
            "message": f"Trade execution failed: {str(e)}"
        }

def execute_best_trade(symbol: str, strategy: str, signal: str, qty: float, entry_price: float, strategy_details: Dict) -> Dict:
    """
    Execute the selected trade with full algorithm metadata.
    """
    try:
        # HARDCODE port 8080
        proxy_url = "http://127.0.0.1:8080"
        
        # Ensure strategy_details is properly formatted
        if not isinstance(strategy_details, dict):
            strategy_details = {"algorithm": strategy, "confidence": "High"}
        
        payload = {
            "symbol": symbol,
            "strategy": strategy,
            "signal": signal,
            "qty": float(qty),
            "entry_price": float(entry_price),
            "strategy_details": strategy_details
        }
        
        log_info(f"Executing trade payload: {payload}")
        
        response = requests.post(f"{proxy_url}/execute_strategy_trade", json=payload, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            log_info(f"Trade executed successfully: {result.get('order', {}).get('id', 'Unknown ID')}")
            return result
        else:
            raise Exception(f"HTTP {response.status_code}: {response.text}")
            
    except Exception as e:
        log_error(f"Trade execution error: {str(e)}")
        # Return simulated execution for demo
        order_id = f"SIM_{symbol}_{datetime.now().strftime('%H%M%S')}"
        return {
            "order": {
                "id": order_id,
                "symbol": symbol,
                "side": signal.lower(),
                "qty": qty,
                "entry_price": entry_price,
                "status": "simulated_fill"
            },
            "algorithm": strategy,
            "strategy_details": {
                **strategy_details,
                "execution_note": "Simulated execution due to API error"
            }
        }

def scan_single_strategy(strategy_name: str, endpoint: str, payload: Dict) -> List[Dict]:
    """
    Helper function to scan a single strategy with error handling.
    """
    try:
        signals = call_strategy_endpoint(endpoint, payload)
        return enhance_signals(signals, strategy_name)
    except Exception as e:
        log_error(f"Error in {strategy_name}: {str(e)}")
        return [] 