# ATLAS Trading Assistant

ATLAS is a sophisticated AI-powered trading assistant that combines ChatGPT-like conversational AI with real-time trading capabilities through Alpaca Markets and Financial Modeling Prep APIs.

## 🚀 Quick Start

### Windows
```bash
# Run the desktop application
python atlas.py

# Or use the batch file
RUN_ATLAS.bat
```

### Manual Server Start
```bash
# Start the server
python main.py

# Then run the desktop app
python atlas.py
```

## 🎯 Core Features

### 1. **AI-Powered Chat Interface**
- Natural language processing with GPT-4
- Conversational trading - just say "Make me $50 today"
- Intelligent context understanding
- Memory of user preferences and past conversations

### 2. **Real-Time Trading Capabilities**
- **Account Management**
  - View real-time buying power and portfolio value
  - Monitor open positions with live P&L
  - Track order history and status
  - Portfolio performance analytics

- **Trade Execution**
  - Natural language trade commands
  - Automatic position sizing based on risk
  - Stop-loss and take-profit management
  - Trade confirmation with plain English metrics

### 3. **Advanced Trading Scanners**

#### Stock Scanners
- **TTM Squeeze Scanner** - Finds stocks in squeeze or firing out
- **EMA Crossover** - 8/21 EMA momentum signals
- **Bollinger Band Reversion** - Mean reversion opportunities
- **RSI Momentum** - Oversold/overbought conditions
- **Volume Spike** - Unusual volume with price movement
- **Donchian Breakout** - Channel breakout signals

#### Crypto Scanners (24/7)
- **MACD Crossover** - Momentum shifts in crypto
- **RSI Extremes** - Crypto oversold/overbought
- **On-Chain NVT** - Network value signals

#### Options Scanners
- **Long Straddle** - High volatility setups
- **Iron Condor** - Range-bound opportunities

#### Fundamental Scanners
- **Insider Buying** - Net insider accumulation
- **Analyst Upgrades** - Recent rating improvements

### 4. **Risk Management**
- Automatic position sizing based on account risk
- Maximum loss limits per trade
- Portfolio diversification through basket trades
- Real-time P&L monitoring

### 5. **Market Intelligence**
- Access to any Financial Modeling Prep endpoint
- Company profiles, financials, ratios
- Economic indicators and market data
- News and earnings calendars
- Insider trading data

## 📋 System Requirements

- Python 3.8 or higher
- Windows/Mac/Linux
- Internet connection
- API Keys (see Configuration)

## 🔧 Configuration

### Required API Keys
Create a `.env` file in the project root:

```env
# Alpaca Trading API (get from https://alpaca.markets)
APCA_API_KEY_ID=your_alpaca_key
APCA_API_SECRET_KEY=your_alpaca_secret

# OpenAI API (get from https://platform.openai.com)
OPENAI_API_KEY=your_openai_key

# Financial Modeling Prep (get from https://financialmodelingprep.com)
FMP_KEY=your_fmp_key
```

## 🏗️ Architecture

### Core Components

1. **atlas.py** - Desktop application with Tkinter UI
2. **main.py** - FastAPI server with all endpoints
3. **llm_brain.py** - GPT-4 integration and conversation management
4. **strategies.py** - All trading strategies and scanners
5. **engine.py** - Trade orchestration and execution
6. **alpaca_integration.py** - Alpaca API wrapper
7. **chat_state_manager.py** - Conversation state management
8. **plain_english_trade_template.py** - Trade formatting
9. **sp500_symbols.py** - Symbol lists
10. **dashboard.py** - Optional Streamlit dashboard

### API Endpoints

#### Chat & AI
- `POST /chat` - Chat with ATLAS AI
- `GET /chat/history/{user_id}` - Get conversation history
- `DELETE /chat/history/{user_id}` - Clear conversation
- `POST /chat/preference` - Update user preferences

#### Trading
- `POST /orchestrate_opportunity` - Find trading opportunities
- `POST /execute_confirmed_trade` - Execute trades
- `POST /close_position` - Close positions

#### Scanners
- `POST /scan_all_strategies` - Master scanner
- `POST /scan_ttm_squeeze` - TTM Squeeze scanner
- `POST /scan_ma_crossover` - EMA crossover
- `POST /scan_bollinger_reversion` - Bollinger bands
- `POST /scan_rsi_momentum` - RSI signals
- `POST /scan_volume_spike` - Volume analysis
- `POST /scan_crypto_macd` - Crypto MACD
- `POST /scan_crypto_rsi` - Crypto RSI

#### Account
- `GET /account/info` - Account information
- `GET /account/positions` - Current positions
- `GET /account/orders` - Order history
- `GET /account/portfolio/history` - Performance history

#### Market Data
- `POST /call_alpaca_api` - Universal Alpaca API access
- `POST /call_fmp_api` - Universal FMP API access
- `GET /market/status` - Market hours status

## 💬 Example Conversations

### Making Money
```
You: Make me $50 today
ATLAS: I found a trade opportunity in AAPL:
• I'll invest $500 to buy 3 shares at $166.67 each
• Maximum possible loss: $10 (stop at $163.33)
• Target profit: $50 (target $183.33)
• Stop-loss: $163.33

Do you confirm?
You: yes
ATLAS: ✅ Order placed successfully!
```

### Scanning Markets
```
You: Find TTM Squeeze setups
ATLAS: Found 3 TTM Squeeze signals:
1. MSFT - Squeeze fired bullish with 2.3% momentum
2. GOOGL - In squeeze for 5 bars, coiling for breakout
3. TSLA - Squeeze fired bearish with -1.8% momentum
```

### Account Info
```
You: What's my buying power?
ATLAS: Your account status:
• Buying Power: $59,790.98
• Portfolio Value: $62,145.32
• Open Positions: 3 (AAPL +$45, MSFT -$12, SPY +$78)
• Day's P&L: +$111 (+0.18%)
```

## 🛡️ Safety Features

- Paper trading mode by default
- Trade confirmation required
- Risk limits enforced
- Stop-loss on every trade
- Maximum position size limits

## 🐛 Troubleshooting

### Server Won't Start
- Check if port 8080 is available
- Verify all dependencies are installed
- Check API keys in .env file

### LLM Not Responding
- Verify OpenAI API key is valid
- Check internet connection
- Restart the application

### Trades Not Executing
- Ensure Alpaca API keys are valid
- Check if market is open
- Verify sufficient buying power

## 📈 Trading Strategies

### Momentum Strategies
- EMA crossovers for trend following
- Donchian breakouts for strong moves
- Volume spike detection

### Mean Reversion
- Bollinger Band touches
- RSI oversold/overbought

### Advanced Setups
- TTM Squeeze for volatility compression
- Options strategies for income

### Fundamental
- Insider buying patterns
- Analyst upgrade momentum

## 🔒 Security

- API keys stored in environment variables
- No sensitive data in code
- Secure HTTPS connections
- Paper trading for safety

## 📝 License

This project is for educational purposes. Always practice responsible trading and never risk more than you can afford to lose.

## 🤝 Support

For issues or questions:
1. Check the troubleshooting section
2. Review the API documentation
3. Ensure all dependencies are installed

---

**Disclaimer**: This is a trading tool. Past performance does not guarantee future results. Always do your own research and trade responsibly. 