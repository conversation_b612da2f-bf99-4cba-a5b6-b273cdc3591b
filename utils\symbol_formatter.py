"""
Symbol Formatter Utility
Ensures proper symbol formatting for different APIs and asset types
"""

import re
from typing import List, Dict, Optional

# Known crypto symbols that Alpaca supports
ALPACA_CRYPTO_SYMBOLS = [
    'BTC', 'ETH', 'ADA', 'SOL', 'DOT', 'AVAX', 'MATIC', 
    'LINK', 'UNI', 'LTC', 'BCH', 'XLM', 'DOGE', 'SHIB',
    'ALGO', 'AAVE', 'SUSHI', 'YFI', 'CRV', 'MKR', 'COMP'
]

# Common stock symbols that might be confused with crypto
STOCK_SYMBOLS_LIKE_CRYPTO = ['IT', 'BR', 'FIS', 'FLT']

def format_symbol(symbol: str, asset_type: str = None) -> str:
    """
    Format symbol correctly for Alpaca API
    
    Args:
        symbol: Raw symbol string
        asset_type: 'stock', 'crypto', 'option', or None for auto-detect
        
    Returns:
        Properly formatted symbol
    """
    # Clean the symbol
    symbol = symbol.upper().strip()
    
    # Remove any existing USD suffix for processing
    base_symbol = symbol.replace('USD', '').replace('/USD', '')
    
    # Auto-detect asset type if not provided
    if asset_type is None:
        asset_type = detect_asset_type(base_symbol)
    
    if asset_type == 'crypto':
        # Crypto symbols need USD suffix for Alpaca
        if base_symbol in ALPACA_CRYPTO_SYMBOLS:
            return f"{base_symbol}USD"
        else:
            # Unknown crypto, return as-is
            return symbol
    
    elif asset_type == 'option':
        # Options have specific format: AAPL210917C00145000
        # Just return as-is for now
        return symbol
    
    else:  # stock or ETF
        # Stocks don't need any suffix
        return base_symbol

def detect_asset_type(symbol: str) -> str:
    """
    Detect the asset type from the symbol
    
    Returns:
        'stock', 'crypto', or 'option'
    """
    # Remove any USD suffix for checking
    base_symbol = symbol.replace('USD', '').replace('/USD', '')
    
    # Check if it's a known crypto
    if base_symbol in ALPACA_CRYPTO_SYMBOLS:
        return 'crypto'
    
    # Check if it looks like an option (has numbers and is long)
    if len(symbol) > 10 and any(char.isdigit() for char in symbol):
        return 'option'
    
    # Default to stock
    return 'stock'

def format_symbol_list(symbols: List[str], asset_type: str = None) -> List[str]:
    """
    Format a list of symbols
    
    Args:
        symbols: List of raw symbols
        asset_type: Asset type for all symbols, or None for auto-detect
        
    Returns:
        List of properly formatted symbols
    """
    return [format_symbol(symbol, asset_type) for symbol in symbols]

def validate_symbol(symbol: str) -> bool:
    """
    Validate if a symbol is in correct format
    
    Returns:
        True if valid, False otherwise
    """
    # Basic validation
    if not symbol or len(symbol) < 1:
        return False
    
    # Check for invalid characters
    if re.search(r'[^A-Z0-9/]', symbol):
        return False
    
    return True

def get_crypto_symbols() -> List[str]:
    """Get list of properly formatted crypto symbols for Alpaca"""
    return [f"{symbol}USD" for symbol in ALPACA_CRYPTO_SYMBOLS]

def get_stock_symbols() -> List[str]:
    """Get list of common stock symbols"""
    # Top S&P 500 stocks
    return [
        'AAPL', 'MSFT', 'AMZN', 'GOOGL', 'META', 'TSLA', 'NVDA', 'JPM',
        'JNJ', 'V', 'PG', 'UNH', 'HD', 'MA', 'DIS', 'PYPL', 'BAC', 'ADBE',
        'NFLX', 'CMCSA', 'XOM', 'VZ', 'INTC', 'T', 'PFE', 'CVX', 'WMT',
        'ABT', 'CRM', 'CSCO', 'PEP', 'TMO', 'ABBV', 'AVGO', 'NKE', 'ACN',
        'COST', 'MRK', 'LLY', 'TXN', 'MDT', 'NEE', 'DHR', 'UNP', 'BMY',
        'QCOM', 'PM', 'LIN', 'AMT', 'LOW', 'ORCL', 'HON', 'IBM', 'SBUX',
        'AMD', 'GS', 'CAT', 'RTX', 'INTU', 'ISRG', 'SPGI', 'DE', 'BKNG',
        'GILD', 'MDLZ', 'BLK', 'ADP', 'TGT', 'MMM', 'SYK', 'VRTX', 'MU'
    ]

def split_by_asset_type(symbols: List[str]) -> Dict[str, List[str]]:
    """
    Split a mixed list of symbols by asset type
    
    Returns:
        Dict with keys 'stocks', 'crypto', 'options'
    """
    result = {
        'stocks': [],
        'crypto': [],
        'options': []
    }
    
    for symbol in symbols:
        asset_type = detect_asset_type(symbol)
        formatted = format_symbol(symbol, asset_type)
        
        if asset_type == 'crypto':
            result['crypto'].append(formatted)
        elif asset_type == 'option':
            result['options'].append(formatted)
        else:
            result['stocks'].append(formatted)
    
    return result

# Symbol mapping for common issues
SYMBOL_FIXES = {
    'DOTUSD': 'DOT/USD',  # Some APIs use slash format
    'MATICUSD': 'MATIC/USD',
    'AVAXUSD': 'AVAX/USD',
    'LINKUSD': 'LINK/USD',
    'UNIUSD': 'UNI/USD',
    'LTCUSD': 'LTC/USD',
    'BCHUSD': 'BCH/USD',
    'XLMUSD': 'XLM/USD',
    'ADAUSD': 'ADA/USD',
    'SOLUSD': 'SOL/USD',
    'ETHUSD': 'ETH/USD',
    'BTCUSD': 'BTC/USD'
}

def fix_crypto_format(symbol: str, target_format: str = 'alpaca') -> str:
    """
    Fix crypto symbol format for different APIs
    
    Args:
        symbol: Input symbol
        target_format: 'alpaca' or 'slash'
        
    Returns:
        Properly formatted symbol
    """
    # Normalize first
    symbol = symbol.upper().strip()
    
    if target_format == 'alpaca':
        # Remove slashes for Alpaca
        return symbol.replace('/', '')
    elif target_format == 'slash':
        # Add slashes if needed
        if '/' not in symbol and symbol.endswith('USD'):
            base = symbol[:-3]
            return f"{base}/USD"
    
    return symbol 