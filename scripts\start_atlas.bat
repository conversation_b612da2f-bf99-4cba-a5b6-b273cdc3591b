@echo off
echo Starting ATLAS Trading Assistant...
echo ====================================

REM Set environment variable
set PROXY_URL=http://127.0.0.1:8081

REM Start FastAPI in background
echo Starting FastAPI on port 8081...
start /min "ATLAS-API" venv\Scripts\python.exe -m uvicorn main:app --port 8081 --host 127.0.0.1

REM Wait for FastAPI to start
echo Waiting for FastAPI to start...
timeout /t 8 /nobreak >nul

REM Start Streamlit
echo Starting Streamlit on port 8501...
start "ATLAS-Dashboard" venv\Scripts\python.exe -m streamlit run dashboard.py --server.port 8501

echo.
echo ATLAS is starting up...
echo FastAPI: http://127.0.0.1:8081/docs
echo Dashboard: http://127.0.0.1:8501
echo.
pause 