"""
Plain English Trade Template for ATLAS
Formats trade proposals in user-friendly language
"""

def format_trade_proposal(trade_data: dict) -> str:
    """
    Format a trade proposal in plain English with the 4 key metrics.
    
    Args:
        trade_data: Dictionary containing trade details
        
    Returns:
        Formatted string for display
    """
    # Extract data
    symbol = trade_data.get("symbol", "Unknown")
    qty = trade_data.get("qty", 0)
    entry_price = trade_data.get("entry_price", 0)
    stop_loss_price = trade_data.get("stop_loss", entry_price * 0.95)  # Default 5% stop
    target_price = trade_data.get("target_price", entry_price * 1.1)  # Default 10% target
    
    # Calculate key metrics
    investment_amount = qty * entry_price
    max_loss = qty * (entry_price - stop_loss_price)
    target_profit = qty * (target_price - entry_price)
    
    # Format the message
    message = f"""I found a trade opportunity in {symbol}:

• **Investment amount**: ${investment_amount:,.2f} (buying {qty} {'shares' if qty >= 1 else 'units'} at ${entry_price:,.2f} each)
• **Maximum possible loss**: ${max_loss:,.2f} (if it drops to stop-loss at ${stop_loss_price:,.2f})
• **Target profit**: ${target_profit:,.2f} (when it reaches ${target_price:,.2f})
• **Stop-loss level**: ${stop_loss_price:,.2f} ({((stop_loss_price/entry_price - 1) * 100):.1f}% below entry)

Do you confirm?"""
    
    return message


def format_basket_proposal(basket_trades: list, total_investment: float, total_risk: float, total_profit: float) -> str:
    """
    Format a basket of trades in plain English.
    
    Args:
        basket_trades: List of individual trade dictionaries
        total_investment: Total capital required
        total_risk: Total maximum loss
        total_profit: Total target profit
        
    Returns:
        Formatted string for display
    """
    message = f"""I've created a diversified basket of {len(basket_trades)} trades:

**Overall Summary:**
• **Total investment**: ${total_investment:,.2f}
• **Maximum possible loss**: ${total_risk:,.2f}
• **Target profit**: ${total_profit:,.2f}
• **Number of trades**: {len(basket_trades)}

**Individual Trades:**
"""
    
    for i, trade in enumerate(basket_trades, 1):
        symbol = trade.get("symbol", "Unknown")
        qty = trade.get("qty", 0)
        entry = trade.get("entry", 0)
        stop = trade.get("stop", 0)
        target = trade.get("target", 0)
        investment = qty * entry
        risk = qty * (entry - stop)
        profit = qty * (target - entry)
        
        message += f"""
{i}. **{symbol}**: 
   - Invest ${investment:,.2f} ({qty} @ ${entry:,.2f})
   - Risk ${risk:,.2f} | Target ${profit:,.2f}
   - Stop at ${stop:,.2f}
"""
    
    message += "\nDo you confirm this basket of trades?"
    
    return message


def format_trade_confirmation(execution_result: dict) -> str:
    """
    Format a trade execution confirmation in plain English.
    
    Args:
        execution_result: Dictionary containing execution details
        
    Returns:
        Formatted confirmation message
    """
    order = execution_result.get("order", {})
    symbol = order.get("symbol", "Unknown")
    qty = order.get("qty", 0)
    price = order.get("entry_price", 0)
    order_id = order.get("id", "Unknown")
    
    message = f"""✅ **Trade Executed Successfully!**

• Bought {qty} {'shares' if qty >= 1 else 'units'} of {symbol}
• Entry price: ${price:,.2f}
• Total invested: ${qty * price:,.2f}
• Order ID: {order_id}

Your stop-loss and take-profit orders have been set automatically. I'll monitor this position and notify you of any important changes."""
    
    return message


def format_trade_rejection() -> str:
    """
    Format a message when user rejects a trade proposal.
    
    Returns:
        Formatted rejection acknowledgment
    """
    return """Understood! No trade was executed. 

Would you like me to:
• Find a different opportunity?
• Adjust the risk parameters?
• Look in different markets?
• Create a diversified basket instead?

Just let me know what you'd prefer!"""


def format_clarification_request(questions: list, context: str) -> str:
    """
    Format a clarification request in plain English.
    
    Args:
        questions: List of questions to ask
        context: Context for why we're asking
        
    Returns:
        Formatted clarification message
    """
    message = f"{context}\n\n"
    
    for i, question in enumerate(questions, 1):
        message += f"{i}. {question}\n"
    
    message += "\nPlease let me know your preferences so I can find the perfect trade for you!"
    
    return message


# Example usage
if __name__ == "__main__":
    # Example trade data
    trade = {
        "symbol": "AAPL",
        "qty": 10,
        "entry_price": 150.00,
        "stop_loss": 145.00,
        "target_price": 160.00
    }
    
    print(format_trade_proposal(trade))
    print("\n" + "="*50 + "\n")
    
    # Example basket
    basket = [
        {"symbol": "AAPL", "qty": 5, "entry": 150, "stop": 145, "target": 160},
        {"symbol": "MSFT", "qty": 3, "entry": 300, "stop": 290, "target": 320},
        {"symbol": "GOOGL", "qty": 2, "entry": 140, "stop": 135, "target": 150}
    ]
    
    total_inv = sum(t["qty"] * t["entry"] for t in basket)
    total_risk = sum(t["qty"] * (t["entry"] - t["stop"]) for t in basket)
    total_profit = sum(t["qty"] * (t["target"] - t["entry"]) for t in basket)
    
    print(format_basket_proposal(basket, total_inv, total_risk, total_profit)) 