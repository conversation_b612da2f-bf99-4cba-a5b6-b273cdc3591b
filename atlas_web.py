"""
ATLAS v2 - Web API Server
FastAPI server for ATLAS trading assistant
"""

from fastapi import FastAP<PERSON>, HTTPException, Body, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from pydantic import BaseModel
from typing import List, Dict, Optional, Any
import uvicorn
import os
from atlas_core import ATLASCore
import asyncio
from datetime import datetime, timedelta
import json
import pandas as pd
import numpy as np
import time
from strategies import detect_ttm_squeeze_signals
from alpaca_integration import get_alpaca_account
from sp500_symbols import get_top_liquid_us_equities_from_alpaca

# Initialize FastAPI app
app = FastAPI(title="ATLAS Trading Assistant", version="2.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize ATLAS core
atlas = ATLASCore()

# Health check state
health_state = {
    "last_check": None,
    "alpaca_status": "unknown",
    "fmp_status": "unknown",
    "last_error": None
}

async def check_api_health():
    """Check connectivity to Alpaca and FMP APIs"""
    try:
        # Check Alpaca
        alpaca_status = await atlas.check_alpaca_connection()
        health_state["alpaca_status"] = "connected" if alpaca_status else "disconnected"
        
        # Check FMP
        fmp_status = await atlas.check_fmp_connection()
        health_state["fmp_status"] = "connected" if fmp_status else "disconnected"
        
        health_state["last_check"] = datetime.now().isoformat()
        health_state["last_error"] = None
    except Exception as e:
        health_state["last_error"] = str(e)
        health_state["last_check"] = datetime.now().isoformat()

# Session storage for pending trades
sessions = {}

# Request models
class ChatRequest(BaseModel):
    message: str
    context: Optional[Dict] = None
    session_id: Optional[str] = None

class TradeRequest(BaseModel):
    symbol: str
    shares: int
    stop_loss: float
    target: float

class ScannerRequest(BaseModel):
    scanner_name: str
    symbols: Optional[List[str]] = None
    limit: Optional[int] = 10
    params: Optional[Dict] = {}

class OpportunityRequest(BaseModel):
    goal: float
    constraints: Optional[Dict] = None

# Global store for squeeze alerts
squeeze_alerts = []
last_alert_time = {}

async def background_squeeze_scanner():
    """Background task to scan for new TTM Squeeze 'first yellow bar' signals using Alpaca data every 15 seconds."""
    global squeeze_alerts, last_alert_time
    symbols = []
    last_symbol_refresh = datetime.utcnow() - timedelta(minutes=31)
    alpaca = get_alpaca_account()
    timeframes = ["1Min", "5Min"]
    cooldown_minutes = 15
    min_score = 7.0
    min_avg_volume = 100000
    while True:
        now = datetime.utcnow()
        # Refresh symbol list every 30 minutes
        if not symbols or (now - last_symbol_refresh).total_seconds() > 1800:
            try:
                # Use a fallback list of popular symbols if the API call fails
                symbols = await get_top_liquid_us_equities_from_alpaca(n=1000)
                last_symbol_refresh = now
            except Exception as e:
                # Fallback to a predefined list of popular symbols
                from sp500_symbols import get_sp500_symbols
                symbols = get_sp500_symbols()[:100]  # Use top 100 S&P 500 symbols
                last_symbol_refresh = now
                print(f"Using fallback symbols due to API error: {e}")
        new_alerts = []
        if not symbols:
            await asyncio.sleep(15)
            continue
        for symbol in symbols:
            try:
                for tf in timeframes:
                    df = await alpaca.aget_historical_bars(symbol, timeframe=tf, limit=60)
                    if df is None or len(df) < 50:
                        continue
                    df = df.reset_index(drop=True)
                    if df['volume'].rolling(20).mean().iloc[-1] < min_avg_volume:
                        continue
                    results = detect_ttm_squeeze_signals(df)
                    if not results:
                        continue
                    latest = results[-1]
                    prev = results[-2]
                    is_yellow = (
                        prev['hist'] < 0 and latest['hist'] < 0 and
                        latest['hist'] > prev['hist'] and
                        abs(latest['hist'] - prev['hist']) > 0.1 and
                        latest['hist'] > prev['hist'] * 0.7
                    )
                    squeeze_on_3bars = all(r['squeeze_on'] for r in results[-4:-1])
                    ema_rising = latest['ema8'] > prev['ema8'] and latest['ema21'] > prev['ema21']
                    vol_spike = latest['volume'] > np.mean([r['volume'] for r in results[-20:]]) * 1.5
                    if (
                        is_yellow and
                        latest['score'] >= min_score and
                        squeeze_on_3bars and
                        ema_rising and
                        vol_spike
                    ):
                        last_time = last_alert_time.get((symbol, tf))
                        if not last_time or (now - last_time).total_seconds() > cooldown_minutes * 60:
                            new_alerts.append({
                                "symbol": symbol,
                                "time": str(latest['time']),
                                "score": float(latest['score']),
                                "close": float(latest['close']),
                                "timeframe": tf
                            })
                            last_alert_time[(symbol, tf)] = now
            except Exception as e:
                continue
        squeeze_alerts = new_alerts
        await asyncio.sleep(15)

@app.on_event("startup")
async def startup_event():
    asyncio.create_task(background_squeeze_scanner())

# API Routes
@app.get("/")
async def read_root():
    return FileResponse('index.html')

@app.get("/api/account")
async def get_account():
    """Get account information"""
    return atlas.get_account_info()

@app.get("/api/positions")
async def get_positions():
    """Get all positions"""
    return atlas.get_positions()

@app.get("/api/analyze/{symbol}")
async def analyze_opportunity(symbol: str):
    """Analyze a trading opportunity"""
    return atlas.analyze_opportunity(symbol)

@app.post("/api/trade")
async def place_trade(request: TradeRequest):
    """Place a trade"""
    return atlas.place_trade(
        request.symbol,
        request.shares,
        request.stop_loss,
        request.target
    )

@app.post("/api/chat")
async def chat(request: ChatRequest):
    """Chat with ATLAS AI"""
    # Get or create session
    session_id = request.session_id or "default"
    if session_id not in sessions:
        sessions[session_id] = {}
    
    # Get current account info and positions for context
    account_info = atlas.get_account_info()
    positions = atlas.get_positions()
    
    # Build context with account info and session data
    context = request.context or {}
    context.update({
        'buying_power': account_info.get('buying_power', 0),
        'portfolio_value': account_info.get('portfolio_value', 0),
        'cash': account_info.get('cash', 0),
        'positions': positions,
        'pending_trade': sessions[session_id].get('pending_trade')
    })
    
    response = atlas.chat_with_ai(request.message, context)
    
    # Check if response contains a pending trade
    if 'Do you confirm this trade?' in response:
        # Extract trade details from context (set by atlas_core)
        if 'pending_trade' in context:
            sessions[session_id]['pending_trade'] = context['pending_trade']
    elif request.message.lower() in ['yes', 'confirm', 'execute', 'do it', 'place the trade']:
        # Clear pending trade after execution
        sessions[session_id].pop('pending_trade', None)
    
    return {"response": response, "session_id": session_id}

# Scanner endpoints
@app.post("/api/scan/ttm_squeeze")
async def scan_ttm_squeeze(request: Dict = Body(default={"limit": 10})):
    """Scan for TTM Squeeze setups"""
    limit = request.get("limit", 10)
    # Run the actual TTM Squeeze scanner
    results = atlas.scan_ttm_squeeze_setups(symbols=None, limit=limit)
    return {"scanner": "ttm_squeeze", "results": results, "count": len(results)}

@app.post("/api/scan/volume_spike")
async def scan_volume_spike(request: Dict = Body(default={"limit": 10})):
    """Scan for volume spike setups"""
    limit = request.get("limit", 10)
    results = atlas.run_scanner("volume_spike", symbols=None, limit=limit)
    return {"scanner": "volume_spike", "results": results, "count": len(results)}

@app.post("/api/scan/iron_condor")
async def scan_iron_condor(request: Dict = Body(default={"limit": 10})):
    """Scan for iron condor setups"""
    limit = request.get("limit", 10)
    results = atlas.run_scanner("iron_condor", symbols=None, limit=limit)
    return {"scanner": "iron_condor", "results": results, "count": len(results)}

@app.post("/api/scan/rsi_momentum")
async def scan_rsi_momentum(request: Dict = Body(default={"limit": 10})):
    """Scan for RSI momentum setups"""
    limit = request.get("limit", 10)
    results = atlas.run_scanner("rsi_momentum", symbols=None, limit=limit)
    return {"scanner": "rsi_momentum", "results": results, "count": len(results)}

@app.post("/api/scan/ma_crossover")
async def scan_ma_crossover(request: Dict = Body(default={"limit": 10})):
    """Scan for MA crossover setups"""
    limit = request.get("limit", 10)
    results = atlas.run_scanner("ma_crossover", symbols=None, limit=limit)
    return {"scanner": "ma_crossover", "results": results, "count": len(results)}

@app.post("/api/scan/insider_buying")
async def scan_insider_buying(request: Dict = Body(default={"limit": 10})):
    """Scan for insider buying activity"""
    limit = request.get("limit", 10)
    results = atlas.run_scanner("insider_buying", symbols=None, limit=limit)
    return {"scanner": "insider_buying", "results": results, "count": len(results)}

@app.post("/api/scan/crypto_macd")
async def scan_crypto_macd(request: Dict = Body(default={"limit": 10})):
    """Scan for crypto MACD signals"""
    limit = request.get("limit", 10)
    results = atlas.run_scanner("crypto_macd", symbols=None, limit=limit)
    return {"scanner": "crypto_macd", "results": results, "count": len(results)}

@app.post("/api/scan/crypto_rsi")
async def scan_crypto_rsi(request: Dict = Body(default={"limit": 10})):
    """Scan for crypto RSI signals"""
    limit = request.get("limit", 10)
    results = atlas.run_scanner("crypto_rsi", symbols=None, limit=limit)
    return {"scanner": "crypto_rsi", "results": results, "count": len(results)}

@app.post("/api/scan/bollinger_reversion")
async def scan_bollinger_reversion(request: Dict = Body(default={"limit": 10})):
    """Scan for Bollinger Band reversion setups"""
    limit = request.get("limit", 10)
    results = atlas.run_scanner("bollinger_reversion", symbols=None, limit=limit)
    return {"scanner": "bollinger_reversion", "results": results, "count": len(results)}

@app.post("/api/scan/donchian_breakout")
async def scan_donchian_breakout(request: Dict = Body(default={"limit": 10})):
    """Scan for Donchian channel breakouts"""
    limit = request.get("limit", 10)
    results = atlas.run_scanner("donchian_breakout", symbols=None, limit=limit)
    return {"scanner": "donchian_breakout", "results": results, "count": len(results)}

@app.post("/api/scan/calendar_spread")
async def scan_calendar_spread(request: Dict = Body(default={"limit": 10})):
    """Scan for calendar spread opportunities"""
    limit = request.get("limit", 10)
    results = atlas.run_scanner("calendar_spread", symbols=None, limit=limit)
    return {"scanner": "calendar_spread", "results": results, "count": len(results)}

@app.post("/api/scan/cash_secured_put")
async def scan_cash_secured_put(request: Dict = Body(default={"limit": 10})):
    """Scan for cash secured put opportunities"""
    limit = request.get("limit", 10)
    results = atlas.run_scanner("cash_secured_put", symbols=None, limit=limit)
    return {"scanner": "cash_secured_put", "results": results, "count": len(results)}

@app.post("/api/scan/{scanner_name}")
async def run_scanner(scanner_name: str, request: ScannerRequest):
    """Run any scanner by name"""
    results = atlas.run_scanner(
        scanner_name,
        request.symbols,
        **request.params
    )
    return {"scanner": scanner_name, "results": results, "count": len(results)}

@app.get("/api/scanners")
async def list_scanners():
    """List all available scanners"""
    return {
        "stock_scanners": [
            "ma_crossover", "bollinger_reversion", "donchian_breakout",
            "rsi_momentum", "volume_spike", "ttm_squeeze"
        ],
        "options_scanners": [
            "long_straddle", "iron_condor", "iron_butterfly",
            "calendar_spread", "diagonal_spread", "covered_call",
            "cash_secured_put", "vertical_spread", "ratio_spread"
        ],
        "crypto_scanners": [
            "crypto_macd", "crypto_rsi", "crypto_onchain"
        ],
        "fundamental_scanners": [
            "insider_buying", "analyst_upgrades"
        ]
    }

@app.post("/api/scan/all")
async def scan_all_markets(asset_classes: Optional[List[str]] = None):
    """Run all scanners based on market conditions"""
    results = atlas.run_all_scanners(asset_classes)
    return results

@app.post("/api/analyze/strategy/{symbol}")
async def analyze_best_strategy(symbol: str):
    """Analyze market conditions and recommend best strategy"""
    return atlas.analyze_best_strategy(symbol)

@app.post("/api/find_opportunity")
async def find_opportunity(request: OpportunityRequest):
    """Find the best trading opportunity to meet profit goal"""
    return atlas.find_opportunity(request.goal, request.constraints)

# Market status endpoint
@app.get("/api/market/status")
async def market_status():
    """Get current market status"""
    from engine import get_market_status
    return get_market_status()

# TTM Scanner Service endpoints
@app.get("/api/scanner/status")
async def scanner_status():
    """Get TTM scanner service status"""
    try:
        from ttm_scanner_service import TTMScannerService
        # Check if scanner results exist
        from pathlib import Path
        results_dir = Path("ttm_scan_results")
        latest_file = results_dir / "latest_results.json"
        
        if latest_file.exists():
            import json
            with open(latest_file, 'r') as f:
                data = json.load(f)
                return {
                    "status": "active",
                    "last_scan": data.get('timestamp'),
                    "total_signals": data.get('total_signals', 0),
                    "message": "Scanner is running in background"
                }
        else:
            return {
                "status": "inactive",
                "message": "No scan results found. Start the scanner service."
            }
    except Exception as e:
        return {"status": "error", "message": str(e)}

@app.get("/api/scanner/results")
async def scanner_results():
    """Get latest TTM scanner results"""
    try:
        from pathlib import Path
        import json
        
        results_dir = Path("ttm_scan_results")
        latest_file = results_dir / "latest_results.json"
        
        if latest_file.exists():
            with open(latest_file, 'r') as f:
                return json.load(f)
        else:
            return {"results": [], "message": "No scan results available"}
    except Exception as e:
        return {"error": str(e)}

@app.post("/api/scanner/start")
async def start_scanner(interval_minutes: int = 10):
    """Start the TTM scanner service"""
    try:
        import subprocess
        import sys
        
        # Start scanner in background process
        subprocess.Popen([sys.executable, "ttm_scanner_service.py", str(interval_minutes)])
        
        return {
            "status": "started",
            "interval_minutes": interval_minutes,
            "message": f"Scanner started with {interval_minutes} minute interval"
        }
    except Exception as e:
        return {"status": "error", "message": str(e)}

@app.get("/api/health")
async def health_check():
    """Get system health status"""
    await check_api_health()
    return {
        "status": "healthy" if health_state["alpaca_status"] == "connected" and health_state["fmp_status"] == "connected" else "degraded",
        "last_check": health_state["last_check"],
        "alpaca_status": health_state["alpaca_status"],
        "fmp_status": health_state["fmp_status"],
        "last_error": health_state["last_error"]
    }

@app.get("/api/squeeze_alerts")
async def get_squeeze_alerts():
    """Return the current list of stocks with a new first yellow bar squeeze signal."""
    return {"alerts": squeeze_alerts}

if __name__ == "__main__":
    # Run the server
    uvicorn.run(app, host="0.0.0.0", port=8081)