"""
Chat State Manager for ATLAS
Maintains conversation state and handles multi-step interactions
"""

from typing import Dict, List, Any, Optional
from enum import Enum
import json
import logging
import re

logger = logging.getLogger(__name__)

class ConversationState(Enum):
    """Enum for conversation states"""
    IDLE = "idle"
    COLLECTING_PROFIT_TARGET = "collecting_profit_target"
    COLLECTING_CAPITAL = "collecting_capital"
    COLLECTING_MAX_LOSS = "collecting_max_loss"
    COLLECTING_MARKETS = "collecting_markets"
    COLLECTING_TIME_HORIZON = "collecting_time_horizon"
    COLLECTING_TRADING_STYLE = "collecting_trading_style"
    CONFIRMING_TRADE = "confirming_trade"
    EXECUTING_TRADE = "executing_trade"
    COMPLETE = "complete"

class ChatStateManager:
    """Manages conversation state for multi-step interactions"""
    
    def __init__(self):
        self.reset()
    
    def reset(self):
        """Reset conversation state"""
        self.state = ConversationState.IDLE
        self.collected_data = {}
        self.pending_trade = None
        self.conversation_history = []
        self.current_question = None
        self.validation_error = None
    
    def get_next_question(self) -> Dict[str, Any]:
        """Get the next question based on current state"""
        questions = {
            ConversationState.COLLECTING_PROFIT_TARGET: {
                "text": "📊 First, what's your profit goal for today?",
                "examples": "Examples: $5 (a coffee), $10 (lunch), $25 (dinner)",
                "help": "This is how much money you want to make. Start small if you're a beginner!",
                "type": "number",
                "field": "goal"
            },
            ConversationState.COLLECTING_CAPITAL: {
                "text": "💰 How much capital are you willing to invest?",
                "examples": "Examples: $100, $500, $1000, or '1k' for $1,000",
                "help": "This is the total amount you're willing to put into trades. We'll only use a portion of this for safety.",
                "type": "number",
                "field": "capital"
            },
            ConversationState.COLLECTING_MAX_LOSS: {
                "text": "🛡️ How much are you comfortable losing on a single trade?",
                "examples": f"Based on your ${self.collected_data.get('goal', 5)} goal:\n• Conservative: ${self.collected_data.get('goal', 5) * 0.2:.2f} (5:1 ratio)\n• Balanced: ${self.collected_data.get('goal', 5) * 0.5:.2f} (2:1 ratio)\n• Aggressive: ${self.collected_data.get('goal', 5):.2f} (1:1 ratio)",
                "help": "This sets your stop-loss to limit losses if the trade goes against you.",
                "type": "number",
                "field": "max_loss"
            },
            ConversationState.COLLECTING_MARKETS: {
                "text": "🌍 Which markets should I scan?",
                "examples": "Options:\n• stocks (US equities like Apple, Microsoft)\n• crypto (Bitcoin, Ethereum - trades 24/7)\n• options (leverage for bigger moves)\n• all (scan everything for best opportunities)",
                "help": "Different markets have different characteristics. 'All' gives you the most opportunities.",
                "type": "choice",
                "field": "markets",
                "choices": ["stocks", "crypto", "options", "all"]
            },
            ConversationState.COLLECTING_TIME_HORIZON: {
                "text": "⏱️ How long do you want to hold positions?",
                "examples": "• minutes (quick scalping trades)\n• hours (day trading)\n• days (swing trading)\n• flexible (whatever works best)",
                "help": "Shorter timeframes need more attention, longer ones are more relaxed.",
                "type": "choice",
                "field": "horizon",
                "choices": ["minutes", "hours", "days", "flexible"]
            },
            ConversationState.COLLECTING_TRADING_STYLE: {
                "text": "🎯 Would you prefer a single trade or a diversified basket?",
                "examples": "• single (one larger position, simpler to manage)\n• basket (5 smaller trades, spreads risk)",
                "help": "Basket trading is recommended for beginners - if one trade loses, others can win!",
                "type": "choice",
                "field": "trading_style",
                "choices": ["single", "basket"]
            }
        }
        
        return questions.get(self.state, {
            "text": "I'm ready to help you trade! Say 'start' to begin or ask me anything.",
            "type": "idle"
        })
    
    def process_user_input(self, user_input: str) -> Dict[str, Any]:
        """Process user input based on current state"""
        user_input = user_input.strip()
        
        # Handle idle state
        if self.state == ConversationState.IDLE:
            if any(trigger in user_input.lower() for trigger in [
                "start", "begin", "trade", "make money", "profit", "help me",
                "make me", "i need", "i want", "help me make", "earn", "generate"
            ]):
                self.state = ConversationState.COLLECTING_PROFIT_TARGET
                return {
                    "type": "question",
                    "question": self.get_next_question(),
                    "progress": "1/6"
                }
            else:
                return {
                    "type": "idle_response",
                    "text": "I'm ATLAS, your trading assistant. Say 'start' to begin setting up trades, or ask me anything about trading!"
                }
        
        # Handle data collection states
        question = self.get_next_question()
        
        if question["type"] == "number":
            # Special handling for risk tolerance - extract number from natural language
            if self.state == ConversationState.COLLECTING_MAX_LOSS:
                # Try to extract a number (dollars) from the user input
                match = re.search(r'\$?(\d+(?:,\d{3})*(?:\.\d+)?)\s*(?:k|K|thousand|Thousand)?', user_input)
                if match:
                    value = float(match.group(1).replace(',', ''))
                    # Handle 'k' or 'thousand' suffix
                    if any(suffix in user_input.lower() for suffix in ['k', 'thousand']):
                        value *= 1000
                    
                    self.collected_data[question["field"]] = value
                    return self._advance_state()
                else:
                    # No numeric value? Ask again, very directly.
                    return {
                        "type": "validation_error",
                        "error": (
                            "I didn't catch a dollar amount. What's the maximum you'd "
                            "be okay losing on a single trade? For example: '10' for $10, "
                            "'100' for $100, or '1k' for $1,000."
                        ),
                        "question": question
                    }
            
            # Handle profit target with similar logic
            elif self.state == ConversationState.COLLECTING_PROFIT_TARGET:
                match = re.search(r'\$?(\d+(?:,\d{3})*(?:\.\d+)?)\s*(?:k|K|thousand|Thousand)?', user_input)
                if match:
                    value = float(match.group(1).replace(',', ''))
                    if any(suffix in user_input.lower() for suffix in ['k', 'thousand']):
                        value *= 1000
                    
                    self.collected_data[question["field"]] = value
                    return self._advance_state()
                else:
                    return {
                        "type": "validation_error",
                        "error": (
                            "Please tell me how much profit you want to make. "
                            "Just type a number like '5' for $5, '100' for $100, etc."
                        ),
                        "question": question
                    }
            
            # Handle capital collection
            elif self.state == ConversationState.COLLECTING_CAPITAL:
                match = re.search(r'\$?(\d+(?:,\d{3})*(?:\.\d+)?)\s*(?:k|K|thousand|Thousand)?', user_input)
                if match:
                    value = float(match.group(1).replace(',', ''))
                    if any(suffix in user_input.lower() for suffix in ['k', 'thousand']):
                        value *= 1000
                    
                    self.collected_data[question["field"]] = value
                    return self._advance_state()
                else:
                    return {
                        "type": "validation_error",
                        "error": (
                            "Please tell me how much you're willing to invest. "
                            "Just a number is fine, like '1000' for $1,000 or '30k' for $30,000."
                        ),
                        "question": question
                    }
            
            # Default number parsing for other fields
            else:
                try:
                    value = self._parse_number(user_input)
                    if value <= 0:
                        return {
                            "type": "validation_error",
                            "error": "Please enter a positive number.",
                            "question": question
                        }
                    
                    self.collected_data[question["field"]] = value
                    return self._advance_state()
                    
                except ValueError:
                    return {
                        "type": "validation_error",
                        "error": f"I didn't understand '{user_input}'. Please enter a number (like 5 or 10.50).",
                        "question": question
                    }
        
        elif question["type"] == "choice":
            choice = user_input.lower()
            
            # Handle special cases
            if question["field"] == "markets" and choice == "all":
                self.collected_data["markets"] = ["stocks", "crypto", "options"]
            elif question["field"] == "markets" and choice in question["choices"]:
                self.collected_data["markets"] = [choice]
            elif question["field"] == "trading_style":
                self.collected_data["basket_mode"] = (choice == "basket")
                self.collected_data["basket_size"] = 5 if choice == "basket" else 1
            elif choice in question["choices"]:
                self.collected_data[question["field"]] = choice
            else:
                # Try to parse partial matches
                matches = [c for c in question["choices"] if c.startswith(choice)]
                if len(matches) == 1:
                    if question["field"] == "markets":
                        self.collected_data["markets"] = [matches[0]]
                    else:
                        self.collected_data[question["field"]] = matches[0]
                else:
                    return {
                        "type": "validation_error",
                        "error": f"Please choose from: {', '.join(question['choices'])}",
                        "question": question
                    }
            
            return self._advance_state()
        
        # Handle trade confirmation
        elif self.state == ConversationState.CONFIRMING_TRADE:
            if user_input.lower() in ["yes", "y", "confirm", "execute", "go"]:
                self.state = ConversationState.EXECUTING_TRADE
                return {
                    "type": "execute_trade",
                    "trade": self.pending_trade
                }
            elif user_input.lower() in ["no", "n", "cancel", "stop"]:
                # Ask what they want to change
                self.state = ConversationState.IDLE
                return {
                    "type": "trade_declined",
                    "text": "No problem! What would you like to change?\n• Different profit target?\n• Adjust risk level?\n• Try different markets?\n• Switch between single/basket mode?\n\nJust tell me what you'd like to adjust, or say 'start' to begin fresh."
                }
            else:
                return {
                    "type": "confirmation_prompt",
                    "text": "Please say 'yes' to execute these trades or 'no' to make changes."
                }
        
        return {
            "type": "error",
            "text": "I'm not sure how to handle that. Let me ask the question again.",
            "question": question
        }
    
    def _parse_number(self, text: str) -> float:
        """Parse a number from user input"""
        # Remove common prefixes
        text = text.replace("$", "").replace(",", "").strip()
        
        # Handle written numbers
        word_to_num = {
            "one": 1, "two": 2, "three": 3, "four": 4, "five": 5,
            "six": 6, "seven": 7, "eight": 8, "nine": 9, "ten": 10,
            "twenty": 20, "fifty": 50, "hundred": 100
        }
        
        text_lower = text.lower()
        for word, num in word_to_num.items():
            if word in text_lower:
                return float(num)
        
        # Try to parse as float
        return float(text)
    
    def _advance_state(self) -> Dict[str, Any]:
        """Advance to the next state"""
        state_progression = {
            ConversationState.COLLECTING_PROFIT_TARGET: ConversationState.COLLECTING_CAPITAL,
            ConversationState.COLLECTING_CAPITAL: ConversationState.COLLECTING_MAX_LOSS,
            ConversationState.COLLECTING_MAX_LOSS: ConversationState.COLLECTING_MARKETS,
            ConversationState.COLLECTING_MARKETS: ConversationState.COLLECTING_TIME_HORIZON,
            ConversationState.COLLECTING_TIME_HORIZON: ConversationState.COLLECTING_TRADING_STYLE,
            ConversationState.COLLECTING_TRADING_STYLE: ConversationState.COMPLETE
        }
        
        self.state = state_progression.get(self.state, ConversationState.IDLE)
        
        if self.state == ConversationState.COMPLETE:
            # All data collected, ready to find trades
            return {
                "type": "data_complete",
                "collected_data": self.collected_data,
                "summary": self._generate_summary()
            }
        else:
            # Get next question
            progress_map = {
                ConversationState.COLLECTING_CAPITAL: "2/6",
                ConversationState.COLLECTING_MAX_LOSS: "3/6",
                ConversationState.COLLECTING_MARKETS: "4/6",
                ConversationState.COLLECTING_TIME_HORIZON: "5/6",
                ConversationState.COLLECTING_TRADING_STYLE: "6/6"
            }
            
            return {
                "type": "question",
                "question": self.get_next_question(),
                "progress": progress_map.get(self.state, "")
            }
    
    def _generate_summary(self) -> str:
        """Generate a summary of collected data"""
        data = self.collected_data
        
        summary = f"✅ Great! Here's what we'll do:\n"
        summary += f"• Target profit: ${data.get('goal', 0):.2f}\n"
        summary += f"• Capital to invest: ${data.get('capital', 0):,.2f}\n"
        summary += f"• Max risk per trade: ${data.get('max_loss', 0):.2f}\n"
        summary += f"• Markets: {', '.join(data.get('markets', []))}\n"
        summary += f"• Time horizon: {data.get('horizon', 'flexible')}\n"
        summary += f"• Style: {'Basket of 5 trades' if data.get('basket_mode') else 'Single trade'}\n"
        
        ratio = data.get('goal', 1) / max(data.get('max_loss', 1), 0.01)
        summary += f"\n📊 Risk/Reward Ratio: {ratio:.1f}:1 "
        
        if ratio >= 3:
            summary += "(Very conservative - great for beginners!)"
        elif ratio >= 2:
            summary += "(Conservative - good balance)"
        else:
            summary += "(Aggressive - higher risk)"
        
        return summary
    
    def set_pending_trade(self, trade_proposal: Dict[str, Any]):
        """Set the pending trade for confirmation"""
        self.pending_trade = trade_proposal
        self.state = ConversationState.CONFIRMING_TRADE
    
    def get_constraints(self) -> Dict[str, Any]:
        """Get the collected constraints for orchestrate_opportunity"""
        return {
            "max_loss": self.collected_data.get("max_loss"),
            "markets": self.collected_data.get("markets", ["stocks", "crypto", "options"]),
            "horizon": self.collected_data.get("horizon", "flexible"),
            "basket_mode": self.collected_data.get("basket_mode", False),
            "basket_size": self.collected_data.get("basket_size", 5)
        }
    
    def get_goal(self) -> float:
        """Get the profit goal"""
        return self.collected_data.get("goal", 0)
    
    def get_state_info(self) -> Dict[str, Any]:
        """Get current state information"""
        return {
            "state": self.state.value,
            "collected_data": self.collected_data,
            "has_pending_trade": self.pending_trade is not None,
            "is_complete": self.state == ConversationState.COMPLETE
        } 