#!/usr/bin/env python3
"""
ATLAS Launcher - Starts the desktop interface
"""

import subprocess
import sys
import time
import requests
import os

def check_server():
    """Check if server is running"""
    try:
        response = requests.get("http://127.0.0.1:8080", timeout=1)
        return response.status_code == 200
    except:
        return False

def main():
    print("=" * 50)
    print("   🤖 ATLAS Trading Assistant")
    print("=" * 50)
    print()
    
    # Check if server is already running
    if check_server():
        print("✅ Server is already running on port 8080")
    else:
        print("🚀 Starting ATLAS server...")
        # Start server in background
        server_process = subprocess.Popen(
            [sys.executable, "main.py"],
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
            creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
        )
        
        # Wait for server to start
        for i in range(30):
            if check_server():
                print("✅ Server started successfully!")
                break
            time.sleep(1)
            if i % 5 == 0:
                print(f"   Waiting for server... ({i}s)")
        else:
            print("❌ Server failed to start")
            return
    
    print()
    print("🖥️  Starting desktop interface...")
    print()
    
    # Start the desktop interface
    try:
        subprocess.run([sys.executable, "atlas.py"])
    except KeyboardInterrupt:
        print("\n\nShutting down ATLAS...")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        input("\nPress Enter to exit...")

if __name__ == "__main__":
    main() 