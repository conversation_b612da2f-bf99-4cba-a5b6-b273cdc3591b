from fastapi import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Query, Body
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv
import requests
import os
from typing import Union, Optional, Dict
# Trading algorithms removed - functionality integrated into strategies.py
from strategies import (
    # Stock strategies
    scan_ma_crossover,
    scan_bollinger_reversion,
    scan_donchian_breakout,
    scan_rsi_momentum,
    scan_volume_spike,
    scan_ttm_squeeze,
    # Options strategies
    scan_long_straddle_setups,
    scan_iron_condor_setups,
    scan_iron_butterfly_setups,
    scan_calendar_spread_setups,
    scan_diagonal_spread_setups,
    scan_covered_call_setups,
    scan_cash_secured_put_setups,
    scan_vertical_spread_setups,
    scan_ratio_spread_setups,
    # Crypto strategies
    scan_crypto_macd,
    scan_crypto_rsi_oversold,
    scan_crypto_onchain_nvt,
    # Fundamental strategies
    scan_insider_buying,
    scan_analyst_upgrades,
    # Intelligent strategy selection
    get_best_strategy_for_conditions,
    analyze_market_conditions,
    # Execution functions
    execute_strategy_trade,
    execute_options_strategy,
    # Master scanner
    scan_all_strategies
)
from sp500_symbols import (
    get_sp500_symbols, 
    get_options_symbols, 
    get_crypto_symbols, 
    get_fundamental_symbols
)
import json
import logging
import traceback
import asyncio
from pydantic import BaseModel
from typing import List
from fastapi.responses import StreamingResponse
import uuid
from datetime import datetime

# Load variables from a local .env file, if present
load_dotenv()

app = FastAPI(title="ATLAS Proxy API", version="1.0.0")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify your domain
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Base URLs for the upstream services
ALP_BASE: str = "https://paper-api.alpaca.markets"
FMP_BASE: str = "https://financialmodelingprep.com/api/v4"

# Read secrets from environment variables (recommended)
ALP_KEY: Optional[str] = os.getenv("APCA_API_KEY_ID")
ALP_SECRET: Optional[str] = os.getenv("APCA_API_SECRET_KEY")
FMP_KEY: Optional[str] = os.getenv("FMP_KEY")

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Initialize LLM Brain (lazy loading)
llm_brain = None

def get_llm_brain():
    """Get or create LLM brain instance"""
    global llm_brain
    if llm_brain is None:
        try:
            from llm_brain import LLMBrain
            llm_brain = LLMBrain()
            logger.info("✅ LLM Brain initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize LLM Brain: {e}")
            logger.info("💡 To enable ChatGPT-like features, set OPENAI_API_KEY environment variable")
    return llm_brain

@app.get("/", response_class=HTMLResponse)
async def read_root():
    """Serve the ATLAS welcome page"""
    return """
    <html>
        <head>
            <title>ATLAS Trading Assistant</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    max-width: 800px;
                    margin: 50px auto;
                    padding: 20px;
                    background-color: #f5f5f5;
                }
                h1 { color: #333; }
                .info { 
                    background: white; 
                    padding: 20px; 
                    border-radius: 8px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    margin: 20px 0;
                }
                .button {
                    display: inline-block;
                    padding: 10px 20px;
                    background: #4CAF50;
                    color: white;
                    text-decoration: none;
                    border-radius: 5px;
                    margin: 10px 5px;
                }
                .button:hover { background: #45a049; }
                code {
                    background: #f0f0f0;
                    padding: 2px 5px;
                    border-radius: 3px;
                }
            </style>
        </head>
        <body>
            <h1>🚀 ATLAS Trading Assistant API</h1>
            
            <div class="info">
                <h2>Welcome to ATLAS!</h2>
                <p>The API server is running successfully on port 8080.</p>
                
                <h3>Desktop Application</h3>
                <p>For the best experience, use the ATLAS Desktop Application:</p>
                <ul>
                    <li>Double-click <code>ATLAS.pyw</code> for the desktop app</li>
                    <li>Or run <code>python atlas_app.py</code> from terminal</li>
                </ul>
                
                <h3>API Endpoints</h3>
                <p>Key endpoints available:</p>
                <ul>
                    <li><code>POST /chat</code> - Chat with ATLAS AI</li>
                    <li><code>GET /account/info</code> - Get account information</li>
                    <li><code>GET /account/positions</code> - View current positions</li>
                    <li><code>POST /scan_all_strategies</code> - Scan for trading opportunities</li>
                    <li><code>POST /orchestrate_opportunity</code> - Find and execute trades</li>
                </ul>
                
                <h3>Quick Links</h3>
                <a href="/docs" class="button">📚 API Documentation</a>
                <a href="/redoc" class="button">📖 Alternative Docs</a>
            </div>
            
            <div class="info">
                <p><em>ATLAS is a sophisticated AI trading assistant. Use the desktop application for the full experience.</em></p>
            </div>
        </body>
    </html>
    """


@app.post("/call_alpaca_api")
def call_alpaca_api(
    path: str = Body(..., description="The REST path under /v2, e.g. 'orders' or 'account'"),
    method: str = Body("GET", description="HTTP method to use"),
    query_params: Optional[dict] = Body(None, description="URL parameters for GET endpoints"),
    body: Optional[dict] = Body(None, description="JSON body for POST/PATCH endpoints"),
    apca_api_key_id: Optional[str] = Header(None, description="Override Alpaca API key ID"),
    apca_api_secret_key: Optional[str] = Header(None, description="Override Alpaca API secret")
):
    """Generic bridge to access ANY Alpaca REST endpoint (trading, account, market data, options, streaming).
    
    This is the universal gateway for all Alpaca API calls, enabling the LLM to access
    any endpoint without specific wrappers.
    """
    # Construct full URL
    if path.startswith("/"):
        url = f"{ALP_BASE}{path}"
    else:
        url = f"{ALP_BASE}/v2/{path}"

    headers = {
        "APCA-API-KEY-ID": apca_api_key_id or ALP_KEY,
        "APCA-API-SECRET-KEY": apca_api_secret_key or ALP_SECRET,
        "Content-Type": "application/json",
    }

    # Ensure we have credentials
    if headers["APCA-API-KEY-ID"] is None or headers["APCA-API-SECRET-KEY"] is None:
        return {
            "error": "Missing Alpaca credentials. Provide them via headers or environment variables."
        }

    try:
        response = requests.request(
            method=method.upper(), 
            url=url, 
            headers=headers, 
            params=query_params, 
            json=body,
            timeout=30
        )
        
        # Return the response with status info
        return {
            "status_code": response.status_code,
            "data": response.json() if response.text else None,
            "success": response.status_code < 400
        }
    except Exception as e:
        logger.error(f"Error calling Alpaca API: {e}")
        return {
            "error": str(e),
            "status_code": 500,
            "success": False
        }


@app.post("/call_fmp_api")
def call_fmp_api(
    endpoint: str = Body(..., description="The FMP endpoint path, e.g. 'profile/AAPL' or 'insider-trading'"),
    query_params: Optional[dict] = Body(None, description="URL parameters, like { 'symbol': 'AAPL' }")
):
    """Generic bridge to access ANY Financial Modeling Prep endpoint.
    
    This universal gateway enables access to all FMP data:
    - Company profiles, financial statements, ratios
    - Insider trades, analyst estimates, ESG scores
    - Economic indicators, forex rates, crypto data
    - News, earnings calendars, IPO calendars
    - And much more...
    """
    if not FMP_KEY:
        return {"error": "Missing FMP_KEY environment variable.", "success": False}

    # Add API key to params
    params = (query_params or {}).copy()
    params["apikey"] = FMP_KEY

    # Construct URL - handle both v3 and v4 endpoints
    if endpoint.startswith("/"):
        endpoint = endpoint[1:]
    
    # Default to v3 unless specified
    if not endpoint.startswith("v"):
        url = f"https://financialmodelingprep.com/api/v3/{endpoint}"
    else:
        url = f"https://financialmodelingprep.com/api/{endpoint}"

    try:
        response = requests.get(url, params=params, timeout=30)
        
        return {
            "status_code": response.status_code,
            "data": response.json() if response.text else None,
            "success": response.status_code < 400
        }
    except Exception as e:
        logger.error(f"Error calling FMP API: {e}")
        return {
            "error": str(e),
            "status_code": 500,
            "success": False
        }


# Keep legacy endpoints for backward compatibility
@app.post("/call_alpaca")
def call_alpaca(
    path: str = Body(..., description="Alpaca REST path, e.g. /v2/orders"),
    method: str = Body("GET", description="HTTP method to use when calling Alpaca"),
    params: Optional[dict] = Body(None, description="Query parameters"),
    body: Optional[dict] = Body(None, description="JSON body for POST/PUT requests"),
    apca_api_key_id: Optional[str] = Header(None, description="Override Alpaca API key ID"),
    apca_api_secret_key: Optional[str] = Header(None, description="Override Alpaca API secret")
):
    """[DEPRECATED] Use /call_alpaca_api instead. Legacy proxy for Alpaca's REST API."""
    return call_alpaca_api(path, method, params, body, apca_api_key_id, apca_api_secret_key)


@app.post("/call_fmp")
def call_fmp(
    path: str = Body(..., description="FMP API path, e.g. /insider-trading"),
    params: Optional[dict] = Body(None, description="Additional query params")
):
    """[DEPRECATED] Use /call_fmp_api instead. Legacy proxy for Financial Modeling Prep's API."""
    return call_fmp_api(path, params)


# =============================================================================
# COMPREHENSIVE STRATEGY ENDPOINTS
# =============================================================================

@app.post("/scan_all_strategies") 
def api_scan_all_strategies(
    symbols: list = Body(None, description="Symbols to scan (defaults to full S&P 500)"),
    asset_classes: Optional[list] = Body(None, description="Asset classes to scan: stocks, crypto, options, fundamentals")
):
    """Master scanner - scan all strategies across asset classes."""
    try:
        if symbols is None:
            # Use comprehensive symbol lists - full S&P 500
            symbols = get_sp500_symbols()  # Full S&P 500 list
        
        # Call the scan function
        result = scan_all_strategies(symbols, asset_classes)
        
        # Ensure we return a proper structure
        return {
            "status": "success",
            "data": result,
            "symbols_count": len(symbols) if symbols else 0,
            "message": "Scan completed successfully"
        }
        
    except Exception as e:
        # Log the full error
        logger.error(f"❌ scan_all_strategies failed: {str(e)}", exc_info=True)
        
        # Always return valid JSON on error
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": f"Scanner error: {str(e)}",
                "data": {},
                "error_type": type(e).__name__,
                "traceback": traceback.format_exc() if os.getenv("DEBUG") else None
            }
        )


# =============================================================================
# STOCK & ETF STRATEGY ENDPOINTS  
# =============================================================================

@app.post("/scan_ma_crossover")
def api_scan_ma_crossover(
    symbols: list = Body(None, description="Symbols to scan (defaults to S&P 500)"),
    short: int = Body(8, description="Short EMA period"),
    long: int = Body(21, description="Long EMA period")
):
    """Scan for EMA crossover signals."""
    try:
        if symbols is None:
            symbols = get_sp500_symbols()
        result = scan_ma_crossover(symbols, short, long)
        return {"status": "success", "signals": result, "count": len(result)}
    except Exception as e:
        logger.error(f"scan_ma_crossover error: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e), "signals": []}
        )


@app.post("/scan_bollinger_reversion")
def api_scan_bollinger_reversion(
    symbols: list = Body(None, description="Symbols to scan (defaults to S&P 500)"),
    period: int = Body(20, description="Bollinger Band period"),
    std_dev: float = Body(2.0, description="Standard deviation multiplier")
):
    """Scan for Bollinger Band mean reversion setups."""
    if symbols is None:
        symbols = get_sp500_symbols()
    return scan_bollinger_reversion(symbols, period, std_dev)


@app.post("/scan_donchian_breakout")
def api_scan_donchian_breakout(
    symbols: list = Body(None, description="Symbols to scan (defaults to S&P 500)"),
    period: int = Body(20, description="Donchian channel period")
):
    """Scan for Donchian channel breakouts."""
    if symbols is None:
        symbols = get_sp500_symbols()
    return scan_donchian_breakout(symbols, period)


@app.post("/scan_rsi_momentum")
def api_scan_rsi_momentum(
    symbols: list = Body(None, description="Symbols to scan (defaults to S&P 500)"),
    oversold: int = Body(30, description="RSI oversold level"),
    midline: int = Body(50, description="RSI midline"),
    overbought: int = Body(70, description="RSI overbought level")
):
    """Scan for RSI momentum signals."""
    if symbols is None:
        symbols = get_sp500_symbols()
    return scan_rsi_momentum(symbols, oversold, midline, overbought)


@app.post("/scan_volume_spike")
def api_scan_volume_spike(
    symbols: list = Body(None, description="Symbols to scan (defaults to S&P 500)"),
    volume_multiplier: float = Body(2.0, description="Volume spike multiplier")
):
    """Scan for volume spike + price momentum."""
    if symbols is None:
        symbols = get_sp500_symbols()
    return scan_volume_spike(symbols, volume_multiplier)


@app.post("/scan_ttm_squeeze")
def api_scan_ttm_squeeze(
    symbols: list = Body(None, description="Symbols to scan (defaults to top 100 S&P 500)"),
    limit: int = Body(10, description="Maximum number of signals to return")
):
    """Scan for TTM Squeeze setups - both active squeezes and squeeze fires."""
    try:
        if symbols is None:
            symbols = get_sp500_symbols()[:100]  # Top 100 for performance
        result = scan_ttm_squeeze(symbols, limit)
        return {"status": "success", "signals": result, "count": len(result)}
    except Exception as e:
        logger.error(f"scan_ttm_squeeze error: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e), "signals": []}
        )


# =============================================================================
# OPTIONS STRATEGY ENDPOINTS
# =============================================================================

@app.post("/scan_long_straddle")
def api_scan_long_straddle(
    symbols: list = Body(None, description="Symbols to scan (defaults to high-liquidity options)"),
    iv_threshold: float = Body(0.5, description="Minimum implied volatility threshold")
):
    """Scan for long straddle opportunities."""
    if symbols is None:
        symbols = get_options_symbols()
    return scan_long_straddle_setups(symbols, iv_threshold)


@app.post("/scan_iron_condor")
def api_scan_iron_condor(
    symbols: list = Body(None, description="Symbols to scan (defaults to high-liquidity options)"),
    iv_rank_min: float = Body(70, description="Minimum IV rank for setup")
):
    """Scan for iron condor opportunities."""
    if symbols is None:
        symbols = get_options_symbols()
    return scan_iron_condor_setups(symbols, iv_rank_min)


@app.post("/scan_iron_butterfly")
def api_scan_iron_butterfly(
    symbols: list = Body(None, description="Symbols to scan (defaults to high-liquidity options)"),
    iv_rank_min: float = Body(75, description="Minimum IV rank for setup")
):
    """Scan for iron butterfly opportunities."""
    if symbols is None:
        symbols = get_options_symbols()
    return scan_iron_butterfly_setups(symbols, iv_rank_min)


@app.post("/scan_calendar_spread")
def api_scan_calendar_spread(
    symbols: list = Body(None, description="Symbols to scan (defaults to high-liquidity options)"),
    iv_skew_min: float = Body(10, description="Minimum IV skew between months")
):
    """Scan for calendar spread opportunities."""
    if symbols is None:
        symbols = get_options_symbols()
    return scan_calendar_spread_setups(symbols, iv_skew_min)


@app.post("/scan_diagonal_spread")
def api_scan_diagonal_spread(
    symbols: list = Body(None, description="Symbols to scan (defaults to high-liquidity options)"),
    trend_strength: float = Body(0.6, description="Minimum trend strength")
):
    """Scan for diagonal spread opportunities."""
    if symbols is None:
        symbols = get_options_symbols()
    return scan_diagonal_spread_setups(symbols, trend_strength)


@app.post("/scan_covered_call")
def api_scan_covered_call(
    symbols: list = Body(None, description="Symbols to scan (defaults to high-liquidity options)"),
    min_premium: float = Body(2.0, description="Minimum premium yield %")
):
    """Scan for covered call opportunities."""
    if symbols is None:
        symbols = get_options_symbols()
    return scan_covered_call_setups(symbols, min_premium)


@app.post("/scan_cash_secured_put")
def api_scan_cash_secured_put(
    symbols: list = Body(None, description="Symbols to scan (defaults to high-liquidity options)"),
    min_premium: float = Body(2.0, description="Minimum premium yield %")
):
    """Scan for cash-secured put opportunities."""
    if symbols is None:
        symbols = get_options_symbols()
    return scan_cash_secured_put_setups(symbols, min_premium)


@app.post("/scan_vertical_spread")
def api_scan_vertical_spread(
    symbols: list = Body(None, description="Symbols to scan (defaults to high-liquidity options)"),
    min_risk_reward: float = Body(2.0, description="Minimum risk/reward ratio")
):
    """Scan for vertical spread opportunities (bull/bear call/put spreads)."""
    if symbols is None:
        symbols = get_options_symbols()
    return scan_vertical_spread_setups(symbols, min_risk_reward)


@app.post("/scan_ratio_spread")
def api_scan_ratio_spread(
    symbols: list = Body(None, description="Symbols to scan (defaults to high-liquidity options)"),
    iv_rank_min: float = Body(60, description="Minimum IV rank for setup")
):
    """Scan for ratio spread opportunities."""
    if symbols is None:
        symbols = get_options_symbols()
    return scan_ratio_spread_setups(symbols, iv_rank_min)


@app.post("/analyze_best_strategy")
def api_analyze_best_strategy(
    symbol: str = Body(..., description="Symbol to analyze"),
    include_options: bool = Body(True, description="Include options strategies in analysis")
):
    """Analyze market conditions and recommend the best trading strategies."""
    try:
        # Analyze current market conditions
        conditions = analyze_market_conditions(symbol)
        
        # Get strategy recommendations
        recommendations = get_best_strategy_for_conditions(conditions)
        
        return {
            "status": "success",
            "symbol": symbol,
            "market_conditions": conditions,
            "recommendations": recommendations["top_recommendations"],
            "analysis_timestamp": recommendations["analysis_timestamp"]
        }
    except Exception as e:
        logger.error(f"analyze_best_strategy error: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)}
        )


# =============================================================================
# CRYPTO STRATEGY ENDPOINTS
# =============================================================================

@app.post("/scan_crypto_macd")
def api_scan_crypto_macd(
    symbols: list = Body(None, description="Crypto symbols to scan (defaults to expanded crypto list)"),
    fast: int = Body(12, description="MACD fast period"),
    slow: int = Body(26, description="MACD slow period"),
    signal: int = Body(9, description="MACD signal period")
):
    """Scan crypto for MACD crossover signals."""
    try:
        if symbols is None:
            symbols = get_crypto_symbols()
        result = scan_crypto_macd(symbols, fast, slow, signal)
        return {"status": "success", "signals": result, "count": len(result)}
    except Exception as e:
        logger.error(f"scan_crypto_macd error: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e), "signals": []}
        )


@app.post("/scan_crypto_rsi")
def api_scan_crypto_rsi(
    symbols: list = Body(None, description="Crypto symbols to scan (defaults to expanded crypto list)"),
    oversold: int = Body(30, description="RSI oversold level"),
    overbought: int = Body(70, description="RSI overbought level")
):
    """Scan crypto for RSI oversold/overbought conditions."""
    if symbols is None:
        symbols = get_crypto_symbols()
    return scan_crypto_rsi_oversold(symbols, oversold, overbought)


@app.post("/scan_crypto_onchain")
def api_scan_crypto_onchain(
    symbols: list = Body(None, description="Crypto symbols to scan (defaults to expanded crypto list)")
):
    """Scan for on-chain NVT signals."""
    if symbols is None:
        symbols = get_crypto_symbols()
    return scan_crypto_onchain_nvt(symbols)


# =============================================================================
# FUNDAMENTAL STRATEGY ENDPOINTS
# =============================================================================

@app.post("/scan_insider_buying")
def api_scan_insider_buying(
    symbols: list = Body(None, description="Stock symbols to scan (defaults to large-cap focus)"),
    days: int = Body(30, description="Lookback period in days")
):
    """Scan for net insider buying activity."""
    if symbols is None:
        symbols = get_fundamental_symbols()
    return scan_insider_buying(symbols, days)


@app.post("/scan_analyst_upgrades")
def api_scan_analyst_upgrades(
    symbols: list = Body(None, description="Stock symbols to scan (defaults to large-cap focus)"),
    lookback_days: int = Body(7, description="Lookback period for upgrades")
):
    """Scan for recent analyst upgrades."""
    if symbols is None:
        symbols = get_fundamental_symbols()
    return scan_analyst_upgrades(symbols, lookback_days)


# =============================================================================
# EXECUTION ENDPOINTS
# =============================================================================

@app.post("/execute_strategy_trade")
def api_execute_strategy_trade(
    symbol: str = Body(..., description="Symbol to trade"),
    strategy: str = Body(..., description="Strategy name"),
    signal: str = Body(..., description="BUY or SELL signal"),
    qty: float = Body(..., description="Quantity to trade"),
    entry_price: float = Body(..., description="Entry price"),
    strategy_details: dict = Body(..., description="Strategy-specific details")
):
    """Execute a strategy trade with full metadata."""
    return execute_strategy_trade(symbol, strategy, signal, qty, entry_price, strategy_details)


@app.post("/execute_options_strategy")
def api_execute_options_strategy(
    symbol: str = Body(..., description="Underlying symbol"),
    strategy_type: str = Body(..., description="Options strategy type"),
    legs: list = Body(..., description="Options legs"),
    strategy_details: dict = Body(..., description="Strategy-specific details")
):
    """Execute an options strategy with multiple legs."""
    return execute_options_strategy(symbol, strategy_type, legs, strategy_details)


# =============================================================================
# LEGACY TTM SQUEEZE ENDPOINTS (kept for compatibility)
# =============================================================================

# Trading algorithm endpoints removed - functionality integrated into strategies
# Use /scan_all_strategies and /orchestrate_opportunity instead


# =============================================================================
# AUTONOMOUS ORCHESTRATOR ENDPOINT
# =============================================================================

@app.post("/orchestrate_opportunity")
def api_orchestrate_opportunity(
    goal: float = Body(..., description="Dollar profit target"),
    capital: float = Body(..., description="Available trading capital"),
    constraints: Dict = Body(None, description="User constraints (markets, max_loss, etc.)"),
    prior_suggestion: Dict = Body(None, description="Previous suggestion if user declined")
):
    """🚀 AUTONOMOUS ENGINE: Scan all markets, pick best trade, execute automatically."""
    try:
        from engine import orchestrate_opportunity
        result = orchestrate_opportunity(goal, capital, constraints, prior_suggestion)
        
        # Ensure proper JSON structure
        if isinstance(result, dict):
            return result
        else:
            return {
                "status": "success",
                "data": result,
                "message": "Orchestration completed"
            }
            
    except Exception as e:
        logger.error(f"❌ orchestrate_opportunity failed: {str(e)}")
        return {
            "status": "error",
            "message": str(e),
            "goal": goal,
            "capital": capital
        }


@app.post("/execute_confirmed_trade")
def api_execute_confirmed_trade(
    trade_proposal: dict = Body(..., description="The trade proposal from orchestrate_opportunity")
):
    """Execute a trade after user confirmation."""
    try:
        from engine import execute_proposed_trade
        result = execute_proposed_trade(trade_proposal)
        return result
    except Exception as e:
        logger.error(f"❌ execute_confirmed_trade failed: {str(e)}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": f"Trade execution failed: {str(e)}"
            }
        )


# =============================================================================
# LLM-POWERED CHAT ENDPOINTS
# =============================================================================

class ChatRequest(BaseModel):
    user_id: str
    message: str
    context: Optional[Dict] = None

class ChatResponse(BaseModel):
    response: str
    actions: Optional[Dict] = None
    status: str = "success"

@app.post("/chat", response_model=ChatResponse)
async def chat_with_llm(request: ChatRequest):
    """
    Main chat endpoint - processes user messages with full LLM capabilities.
    Maintains conversation context and can execute trading functions.
    """
    try:
        brain = get_llm_brain()
        if not brain:
            # Provide intelligent fallback responses for common questions
            message_lower = request.message.lower()
            
            # Handle "what is basic mode" and similar questions
            if "basic mode" in message_lower or "what mode" in message_lower:
                return ChatResponse(
                    response=(
                        "Basic mode means I'm running without my full AI capabilities. "
                        "In this mode, I can still help you find trading opportunities using my built-in strategies, "
                        "but I can't have natural conversations or understand complex questions like I normally would.\n\n"
                        "To enable my full ChatGPT-like capabilities, you need to set up an OpenAI API key. "
                        "Once that's done, I'll be able to:\n"
                        "• Have natural conversations about trading\n"
                        "• Explain complex concepts in simple terms\n"
                        "• Remember your preferences across sessions\n"
                        "• Provide personalized trading advice\n\n"
                        "For now, I can still help you make trades! Just say 'start' to begin."
                    ),
                    actions={"mode": "basic"},
                    status="limited"
                )
            
            # Handle trading requests
            elif any(phrase in message_lower for phrase in ["make me", "i need", "profit", "trade", "start"]):
                return ChatResponse(
                    response=(
                        "I'm in basic mode, but I can still help you trade! "
                        "Let me guide you through setting up a profitable trade.\n\n"
                        "To get started, I'll need to know:\n"
                        "1. How much profit you want to make\n"
                        "2. Your risk tolerance\n"
                        "3. Which markets to scan\n\n"
                        "Say 'start' to begin the setup process."
                    ),
                    actions={"fallback": "orchestrate_opportunity"},
                    status="limited"
                )
            
            # Default fallback
            return ChatResponse(
                response=(
                    "I'm currently running in basic mode without full AI capabilities. "
                    "I can help you find and execute trades, but I can't have complex conversations.\n\n"
                    "Say 'start' to begin setting up trades, or ask me 'what is basic mode?' to learn more."
                ),
                actions={"fallback": "orchestrate_opportunity"},
                status="limited"
            )
        
        # Process message with LLM
        response, actions = await brain.process_message(
            user_id=request.user_id,
            message=request.message,
            context=request.context
        )
        
        return ChatResponse(
            response=response,
            actions=actions,
            status="success"
        )
        
    except Exception as e:
        logger.error(f"Chat error: {e}", exc_info=True)
        return ChatResponse(
            response="I encountered an error processing your message. Please try again.",
            actions={"error": str(e)},
            status="error"
        )

@app.get("/chat/stream")
async def chat_stream(user_id: str, message: str):
    """
    Streaming chat endpoint - returns responses as they're generated.
    Provides real-time "thinking" experience like ChatGPT.
    """
    try:
        brain = get_llm_brain()
        if not brain:
            return JSONResponse(
                status_code=503,
                content={"error": "LLM not available"}
            )
        
        async def generate():
            # Send initial thinking message
            yield f"data: {json.dumps({'type': 'thinking', 'content': 'Analyzing your request...'})}\n\n"
            
            # Get or create conversation
            if user_id not in brain.conversations:
                from llm_brain import Conversation, Message, MessageRole
                brain.conversations[user_id] = Conversation(
                    messages=[Message(MessageRole.SYSTEM, brain.system_prompt)],
                    user_id=user_id,
                    created_at=datetime.now(),
                    context={}
                )
            
            conversation = brain.conversations[user_id]
            
            # Add user message
            conversation.messages.append(Message(MessageRole.USER, message))
            
            # Prepare messages for API
            messages = [{"role": msg.role.value, "content": msg.content} for msg in conversation.messages]
            
            # Create streaming response with function detection
            stream = brain.client.chat.completions.create(
                model=brain.model,
                messages=messages,
                functions=brain.functions,
                function_call="auto",
                stream=True,
                temperature=0.7,
                max_tokens=brain.response_buffer
            )
            
            # Collect response and detect function calls
            collected_content = []
            function_call = None
            
            for chunk in stream:
                # Check for function call
                if chunk.choices[0].delta.function_call:
                    if not function_call:
                        function_call = {"name": "", "arguments": ""}
                    
                    if chunk.choices[0].delta.function_call.name:
                        function_call["name"] = chunk.choices[0].delta.function_call.name
                        # Send function detection event
                        yield f"data: {json.dumps({'type': 'function_call', 'name': function_call['name']})}\n\n"
                    
                    if chunk.choices[0].delta.function_call.arguments:
                        function_call["arguments"] += chunk.choices[0].delta.function_call.arguments
                
                # Stream content
                if chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    collected_content.append(content)
                    yield f"data: {json.dumps({'type': 'content', 'content': content})}\n\n"
            
            # If function call was detected, parse and send arguments
            if function_call and function_call.get("arguments"):
                try:
                    args = json.loads(function_call["arguments"])
                    yield f"data: {json.dumps({'type': 'function_args', 'args': args})}\n\n"
                    
                    # Send progress event for function execution
                    yield f"data: {json.dumps({'type': 'progress', 'title': 'Executing Trade Analysis', 'step': 1, 'total': 3, 'message': 'Scanning markets for opportunities...'})}\n\n"
                    
                    # Execute the function
                    result = await brain._execute_function(function_call["name"], args, conversation)
                    
                    # Send progress update
                    yield f"data: {json.dumps({'type': 'progress', 'title': 'Executing Trade Analysis', 'step': 2, 'total': 3, 'message': 'Calculating optimal position sizes...'})}\n\n"
                    
                    # Add function result to conversation
                    conversation.messages.append(
                        Message(MessageRole.FUNCTION, json.dumps(result), name=function_call["name"])
                    )
                    
                    # Send final progress
                    yield f"data: {json.dumps({'type': 'progress', 'title': 'Executing Trade Analysis', 'step': 3, 'total': 3, 'message': 'Preparing trade proposal...'})}\n\n"
                    
                    # Get follow-up response
                    follow_up_messages = []
                    for msg in conversation.messages:
                        msg_dict = {"role": msg.role.value, "content": msg.content}
                        if msg.name:
                            msg_dict["name"] = msg.name
                        follow_up_messages.append(msg_dict)
                    
                    follow_up_stream = brain.client.chat.completions.create(
                        model=brain.model,
                        messages=follow_up_messages,
                        stream=True,
                        temperature=0.7,
                        max_tokens=brain.response_buffer
                    )
                    
                    # Stream follow-up response
                    for chunk in follow_up_stream:
                        if chunk.choices[0].delta.content:
                            content = chunk.choices[0].delta.content
                            collected_content.append(content)
                            yield f"data: {json.dumps({'type': 'content', 'content': content})}\n\n"
                    
                    # Send completion with function result
                    yield f"data: {json.dumps({'type': 'done', 'function_result': {'function_called': function_call['name'], 'function_result': result}})}\n\n"
                except Exception as e:
                    logger.error(f"Function execution error: {e}")
                    yield f"data: {json.dumps({'type': 'error', 'error': str(e)})}\n\n"
            else:
                # No function call, just send completion
                yield f"data: {json.dumps({'type': 'done'})}\n\n"
            
            # Save assistant response to conversation
            full_response = "".join(collected_content)
            conversation.messages.append(Message(MessageRole.ASSISTANT, full_response))
        
        return StreamingResponse(
            generate(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
            }
        )
        
    except Exception as e:
        logger.error(f"Stream error: {e}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={"error": str(e)}
        )

@app.get("/chat/history/{user_id}")
async def get_chat_history(user_id: str):
    """Get conversation history for a user."""
    try:
        brain = get_llm_brain()
        if not brain:
            return {"history": [], "status": "no_llm"}
        
        history = brain.get_conversation_history(user_id)
        return {
            "history": history,
            "status": "success",
            "count": len(history)
        }
        
    except Exception as e:
        logger.error(f"History error: {e}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={"error": str(e)}
        )

@app.delete("/chat/history/{user_id}")
async def clear_chat_history(user_id: str):
    """Clear conversation history for a user."""
    try:
        brain = get_llm_brain()
        if not brain:
            return {"status": "no_llm"}
        
        brain.clear_conversation(user_id)
        return {"status": "success", "message": "Conversation history cleared"}
        
    except Exception as e:
        logger.error(f"Clear history error: {e}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={"error": str(e)}
        )

@app.post("/chat/preference")
async def update_preference(
    user_id: str = Body(...),
    preference_type: str = Body(...),
    value: str = Body(...)
):
    """Update user preferences that persist across conversations."""
    try:
        brain = get_llm_brain()
        if not brain:
            return {"status": "no_llm"}
        
        # Update preference
        if user_id not in brain.user_preferences:
            brain.user_preferences[user_id] = {}
        brain.user_preferences[user_id][preference_type] = value
        
        return {
            "status": "success",
            "preference_type": preference_type,
            "value": value
        }
        
    except Exception as e:
        logger.error(f"Preference error: {e}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={"error": str(e)}
        )

@app.get("/chat/export/{user_id}")
async def export_user_data(user_id: str):
    """Export all user data including preferences and conversation history."""
    try:
        brain = get_llm_brain()
        if not brain:
            return {"status": "no_llm"}
        
        data = brain.export_user_data(user_id)
        return data
        
    except Exception as e:
        logger.error(f"Export error: {e}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={"error": str(e)}
        )

# =============================================================================
# ALPACA ACCOUNT ENDPOINTS
# =============================================================================

@app.get("/account/info")
def get_account_info():
    """Get comprehensive Alpaca account information"""
    try:
        from alpaca_integration import get_alpaca_account
        alpaca = get_alpaca_account()
        return alpaca.get_account_info()
    except Exception as e:
        logger.error(f"Error getting account info: {str(e)}")
        return {"error": str(e)}

@app.get("/account/positions")
def get_account_positions():
    """Get all current positions"""
    try:
        from alpaca_integration import get_alpaca_account
        alpaca = get_alpaca_account()
        return alpaca.get_positions()
    except Exception as e:
        logger.error(f"Error getting positions: {str(e)}")
        return {"error": str(e)}

@app.get("/account/orders")
def get_account_orders(status: str = Query("all", description="Order status: all, open, closed")):
    """Get orders with specified status"""
    try:
        from alpaca_integration import get_alpaca_account
        alpaca = get_alpaca_account()
        return alpaca.get_orders(status=status)
    except Exception as e:
        logger.error(f"Error getting orders: {str(e)}")
        return {"error": str(e)}

@app.get("/account/portfolio/history")
def get_portfolio_history(
    period: str = Query("1D", description="Period: 1D, 1W, 1M, 3M, 1Y, all"),
    timeframe: str = Query("1Min", description="Timeframe: 1Min, 5Min, 15Min, 1H, 1D")
):
    """Get portfolio value history"""
    try:
        from alpaca_integration import get_alpaca_account
        alpaca = get_alpaca_account()
        return alpaca.get_portfolio_history(period=period, timeframe=timeframe)
    except Exception as e:
        logger.error(f"Error getting portfolio history: {str(e)}")
        return {"error": str(e)}

@app.get("/account/summary")
def get_account_summary():
    """Get comprehensive account summary including positions, orders, and performance"""
    try:
        from alpaca_integration import get_alpaca_account
        alpaca = get_alpaca_account()
        return alpaca.get_account_summary()
    except Exception as e:
        logger.error(f"Error getting account summary: {str(e)}")
        return {"error": str(e)}

@app.get("/account/activities")
def get_account_activities(
    activity_types: Optional[str] = Query(None, description="Comma-separated activity types")
):
    """Get account activities (trades, dividends, etc.)"""
    try:
        from alpaca_integration import get_alpaca_account
        alpaca = get_alpaca_account()
        types_list = activity_types.split(",") if activity_types else None
        return alpaca.get_account_activities(activity_types=types_list)
    except Exception as e:
        logger.error(f"Error getting account activities: {str(e)}")
        return {"error": str(e)}

@app.get("/market/status")
def get_market_status():
    """Get current market status (open/closed)"""
    try:
        from alpaca_integration import get_alpaca_account
        alpaca = get_alpaca_account()
        return alpaca.get_clock()
    except Exception as e:
        logger.error(f"Error getting market status: {str(e)}")
        return {"error": str(e)}

@app.post("/close_position")
def close_position(
    symbol: str = Body(..., description="Symbol to close position for")
):
    """Close a position for a specific symbol"""
    try:
        # Use the Alpaca API to close the position
        response = requests.delete(
            f"{ALP_BASE}/v2/positions/{symbol}",
            headers={
                "APCA-API-KEY-ID": ALP_KEY,
                "APCA-API-SECRET-KEY": ALP_SECRET,
                "Content-Type": "application/json"
            }
        )
        
        if response.status_code == 200:
            return {
                "status": "success",
                "message": f"Successfully closed position for {symbol}",
                "data": response.json()
            }
        else:
            return JSONResponse(
                status_code=response.status_code,
                content={
                    "status": "error",
                    "message": f"Failed to close position: {response.text}"
                }
            )
    except Exception as e:
        logger.error(f"Error closing position for {symbol}: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": str(e)}
        )


if __name__ == "__main__":
    # Support `python main.py` for quick local dev (not for prod)
    import uvicorn

    uvicorn.run("main:app", host="127.0.0.1", port=8080, reload=True) 