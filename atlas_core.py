"""
ATLAS v2 - Core Trading Assistant
A simple, working AI trading assistant that helps you make money
"""

import os
import json
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import alpaca_trade_api as tradeapi
from openai import OpenAI
from dotenv import load_dotenv

# Import all strategies and engine functionality
from strategies import (
    # Stock scanners
    scan_ma_crossover, scan_bollinger_reversion, scan_donchian_breakout,
    scan_rsi_momentum, scan_volume_spike, scan_ttm_squeeze,
    # Options scanners
    scan_long_straddle_setups, scan_iron_condor_setups, scan_iron_butterfly_setups,
    scan_calendar_spread_setups, scan_diagonal_spread_setups, scan_covered_call_setups,
    scan_cash_secured_put_setups, scan_vertical_spread_setups, scan_ratio_spread_setups,
    # Crypto scanners
    scan_crypto_macd, scan_crypto_rsi_oversold, scan_crypto_onchain_nvt,
    # Fundamental scanners
    scan_insider_buying, scan_analyst_upgrades,
    # Analysis functions
    analyze_market_conditions, get_best_strategy_for_conditions,
    scan_all_strategies, scan_all_markets,
    # Technical indicators
    compute_ema, compute_rsi, compute_macd, compute_bollinger_bands,
    calculate_ttm_squeeze,
    # Data functions
    get_market_data
)

from sp500_symbols import get_sp500_symbols

from engine import (
    orchestrate_opportunity, enhance_signals, filter_by_constraints,
    calculate_position_size, is_stock_market_open, get_market_status
)

from comprehensive_api import ComprehensiveAPI
from alpaca_integration import AlpacaAccount

# Load environment variables
load_dotenv()

# Configure logging
logger = logging.getLogger("ATLAS_CORE")

class ATLASCore:
    """The brain of ATLAS - handles all trading logic and AI interactions"""
    
    def __init__(self):
        # Initialize Comprehensive API with ALL endpoints
        self.api = ComprehensiveAPI()
        
        # Initialize OpenAI
        openai_key = os.getenv("OPENAI_API_KEY")
        if not openai_key:
            raise ValueError("OPENAI_API_KEY environment variable is required")
        self.openai_client = OpenAI(api_key=openai_key)
        
        # Initialize Alpaca account
        self.alpaca = AlpacaAccount()
        
        # Trading parameters
        self.risk_per_trade = 0.02  # 2% risk per trade
        self.default_profit_target = 1.5  # 1.5:1 risk/reward ratio
        
        # Scanner functions mapping
        self.scanners = {
            # Stock scanners
            'ma_crossover': scan_ma_crossover,
            'bollinger_reversion': scan_bollinger_reversion,
            'donchian_breakout': scan_donchian_breakout,
            'rsi_momentum': scan_rsi_momentum,
            'volume_spike': scan_volume_spike,
            'ttm_squeeze': scan_ttm_squeeze,
            # Options scanners
            'long_straddle': scan_long_straddle_setups,
            'iron_condor': scan_iron_condor_setups,
            'iron_butterfly': scan_iron_butterfly_setups,
            'calendar_spread': scan_calendar_spread_setups,
            'diagonal_spread': scan_diagonal_spread_setups,
            'covered_call': scan_covered_call_setups,
            'cash_secured_put': scan_cash_secured_put_setups,
            'vertical_spread': scan_vertical_spread_setups,
            'ratio_spread': scan_ratio_spread_setups,
            # Crypto scanners
            'crypto_macd': scan_crypto_macd,
            'crypto_rsi': scan_crypto_rsi_oversold,
            'crypto_onchain': scan_crypto_onchain_nvt,
            # Fundamental scanners
            'insider_buying': scan_insider_buying,
            'analyst_upgrades': scan_analyst_upgrades
        }
        
        print("✅ ATLAS Core initialized with FULL API access and ALL scanners!")
        
    async def check_alpaca_connection(self) -> bool:
        """Check if Alpaca API is accessible"""
        try:
            account = self.alpaca.get_account_info()
            return account is not None and 'error' not in account
        except Exception as e:
            logger.error(f"Alpaca connection check failed: {e}")
            return False

    async def check_fmp_connection(self) -> bool:
        """Check if FMP API is accessible"""
        try:
            # Try to get a simple quote to test connection
            result = self.api.fmp_quote('AAPL')
            return isinstance(result, list) and len(result) > 0
        except Exception as e:
            logger.error(f"FMP connection check failed: {e}")
            return False

    def get_account_info(self) -> Dict:
        """Get current account information"""
        try:
            account = self.alpaca.get_account_info()
            if 'error' in account:
                logger.error(f"Failed to get account info: {account['error']}")
                return {
                    'status': 'error',
                    'error': account['error'],
                    'buying_power': 0,
                    'portfolio_value': 0,
                    'cash': 0
                }
            return {
                'status': 'active',
                'buying_power': float(account.get('buying_power', 0)),
                'portfolio_value': float(account.get('portfolio_value', 0)),
                'cash': float(account.get('cash', 0)),
                'day_trade_count': account.get('daytrade_count', 0),
                'pattern_day_trader': account.get('pattern_day_trader', False)
            }
        except Exception as e:
            logger.error(f"Failed to get account info: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'buying_power': 0,
                'portfolio_value': 0,
                'cash': 0
            }
    
    def get_positions(self) -> List[Dict]:
        """Get all current positions"""
        try:
            positions = self.alpaca.get_positions()
            if isinstance(positions, list):
                return [{
                    'symbol': pos.get('symbol', ''),
                    'qty': float(pos.get('qty', 0)),
                    'avg_entry_price': float(pos.get('avg_entry_price', 0)),
                    'current_price': float(pos.get('current_price', 0)),
                    'market_value': float(pos.get('market_value', 0)),
                    'unrealized_pl': float(pos.get('unrealized_pl', 0)),
                    'unrealized_plpc': float(pos.get('unrealized_plpc', 0)) * 100  # Convert to percentage
                } for pos in positions]
            return []
        except Exception as e:
            logger.error(f"Failed to get positions: {e}")
            return []
    
    def analyze_opportunity(self, symbol: str) -> Dict:
        """Analyze a trading opportunity"""
        try:
            # Get latest price
            bars = self.alpaca.get_latest_bar(symbol)
            current_price = bars.c
            
            # Get account info for position sizing
            account = self.get_account_info()
            buying_power = account.get('buying_power', 0)
            
            # Calculate position size (2% risk)
            risk_amount = buying_power * self.risk_per_trade
            
            # Simple momentum strategy - just for demonstration
            # In real implementation, this would use technical indicators
            stop_loss_pct = 0.02  # 2% stop loss
            target_pct = 0.03     # 3% profit target
            
            stop_loss_price = current_price * (1 - stop_loss_pct)
            target_price = current_price * (1 + target_pct)
            
            # Calculate shares based on risk
            shares = int(risk_amount / (current_price - stop_loss_price))
            investment = shares * current_price
            
            return {
                'symbol': symbol,
                'current_price': current_price,
                'shares': shares,
                'investment': investment,
                'stop_loss': stop_loss_price,
                'target': target_price,
                'max_loss': shares * (current_price - stop_loss_price),
                'potential_profit': shares * (target_price - current_price),
                'risk_reward_ratio': (target_price - current_price) / (current_price - stop_loss_price)
            }
        except Exception as e:
            return {'error': str(e)}
    
    def place_trade(self, symbol: str, shares: int, stop_loss: float, target: float) -> Dict:
        """Place a trade with stop loss and take profit"""
        try:
            # Place market order
            order = self.alpaca.submit_order(
                symbol=symbol,
                qty=shares,
                side='buy',
                type='market',
                time_in_force='day'
            )
            
            # Place stop loss order
            stop_order = self.alpaca.submit_order(
                symbol=symbol,
                qty=shares,
                side='sell',
                type='stop',
                time_in_force='gtc',
                stop_price=stop_loss
            )
            
            # Place take profit order
            limit_order = self.alpaca.submit_order(
                symbol=symbol,
                qty=shares,
                side='sell',
                type='limit',
                time_in_force='gtc',
                limit_price=target
            )
            
            return {
                'success': True,
                'order_id': order.id,
                'stop_order_id': stop_order.id,
                'limit_order_id': limit_order.id
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def chat_with_ai(self, message: str, context: Dict = None) -> str:
        """Chat with AI about trading"""
        try:
            # Check if user is asking about insider trading
            insider_keywords = ['insider', 'senate', 'congress', 'politician', 'ceo', 'executive']
            if any(keyword in message.lower() for keyword in insider_keywords):
                # Fetch insider trading data
                insider_data = []
                
                # Get recent insider trades
                insider_trades = self.api.get_insider_trades(limit=10)
                senate_trades = self.api.get_senate_trades(limit=5)
                house_trades = self.api.get_house_trades(limit=5)
                
                # Format insider data
                insider_context = "RECENT INSIDER TRADE ACTIVITY:\n"
                
                # Check if insider_trades is a list (not an error dict)
                if isinstance(insider_trades, list) and insider_trades:
                    insider_context += "\n📊 CORPORATE INSIDER TRADES:\n"
                    for trade in insider_trades[:5]:
                        insider_context += f"\n- {trade.get('reportingName', 'Unknown')} ({trade.get('typeOfOwner', '')})"
                        insider_context += f"\n  Company: {trade.get('symbol', '')} - {trade.get('securityName', '')}"
                        insider_context += f"\n  Action: {trade.get('transactionType', '')} {trade.get('securitiesTransacted', 0):,} shares"
                        insider_context += f"\n  Price: ${trade.get('price', 0):.2f}"
                        insider_context += f"\n  Date: {trade.get('transactionDate', '')}\n"
                
                if isinstance(senate_trades, list) and senate_trades:
                    insider_context += "\n🏛️ SENATE TRADES:\n"
                    for trade in senate_trades[:3]:
                        insider_context += f"\n- Senator {trade.get('senator', 'Unknown')}"
                        insider_context += f"\n  Stock: {trade.get('ticker', '')} - {trade.get('assetDescription', '')}"
                        insider_context += f"\n  Type: {trade.get('type', '')}"
                        insider_context += f"\n  Amount: {trade.get('amount', '')}"
                        insider_context += f"\n  Date: {trade.get('transactionDate', '')}\n"
                
                if isinstance(house_trades, list) and house_trades:
                    insider_context += "\n🏛️ HOUSE TRADES:\n"
                    for trade in house_trades[:3]:
                        insider_context += f"\n- Rep. {trade.get('representative', 'Unknown')} ({trade.get('party', '')})"
                        insider_context += f"\n  Stock: {trade.get('ticker', '')} - {trade.get('assetDescription', '')}"
                        insider_context += f"\n  Type: {trade.get('transactionType', '')}"
                        insider_context += f"\n  Amount: {trade.get('amount', '')}"
                        insider_context += f"\n  Date: {trade.get('transactionDate', '')}\n"
                
                # Add to message
                enhanced_message = f"{message}\n\n{insider_context}"
                
                # Build system message for insider analysis
                system_message = """You are ATLAS, an advanced AI trading assistant with insider trading data access.
                
                You have just fetched REAL insider trading data. Analyze it and provide:
                1. Most significant insider trades and what they signal
                2. Patterns in insider buying/selling
                3. Specific stocks with heavy insider activity
                4. Trading opportunities based on following smart money
                
                Focus on actionable insights. If insiders are buying heavily, that's often a bullish signal."""
                
                response = self.openai_client.chat.completions.create(
                    model="gpt-4",
                    messages=[
                        {"role": "system", "content": system_message},
                        {"role": "user", "content": enhanced_message}
                    ],
                    temperature=0.7,
                    max_tokens=800
                )
                
                return response.choices[0].message.content
            
            # Check if user is asking about news
            news_keywords = ['news', 'trump', 'elon', 'musk', 'announcement', 'latest', 'recent', 'today', 'breaking']
            if any(keyword in message.lower() for keyword in news_keywords):
                # Fetch real news
                news_results = []
                
                # Search for specific topics mentioned
                if 'trump' in message.lower():
                    trump_news = self.api.get_news(search='Trump', limit=3)
                    if isinstance(trump_news, list):
                        news_results.extend(trump_news)
                
                if 'elon' in message.lower() or 'musk' in message.lower():
                    musk_news = self.api.get_news(search='Elon Musk', limit=3)
                    if isinstance(musk_news, list):
                        news_results.extend(musk_news)
                    tesla_news = self.api.get_news(tickers='TSLA', limit=2)
                    if isinstance(tesla_news, list):
                        news_results.extend(tesla_news)
                
                # If no specific person mentioned, get general market news
                if not news_results:
                    general_news = self.api.get_news(limit=5)
                    if isinstance(general_news, list):
                        news_results = general_news
                
                # Format news for the AI
                news_context = "REAL-TIME NEWS:\n"
                # Ensure news_results is a list
                if isinstance(news_results, list):
                    for article in news_results[:5]:  # Limit to 5 articles
                        news_context += f"\n- {article.get('title', 'No title')}"
                        news_context += f"\n  Source: {article.get('site', article.get('source', 'Unknown'))}"
                        news_context += f"\n  Time: {article.get('publishedDate', article.get('created_at', 'Unknown'))}"
                        news_context += f"\n  Summary: {article.get('text', article.get('summary', 'No summary'))[:200]}..."
                        news_context += f"\n  URL: {article.get('url', '')}"
                        news_context += f"\n  Symbols: {article.get('symbol', article.get('tickers', 'N/A'))}\n"
                
                # Add news context to the message
                enhanced_message = f"{message}\n\n{news_context}"
                
                # Build system message for news analysis
                system_message = """You are ATLAS, an advanced AI trading assistant with real-time news access.
                
                You have just fetched REAL news articles. Analyze them and provide:
                1. Key takeaways from the news
                2. Potential market impact
                3. Specific stocks that might be affected
                4. Trading opportunities based on the news
                
                Be specific about the actual news content, not generic. Reference the actual headlines and details."""
                
                response = self.openai_client.chat.completions.create(
                    model="gpt-4",
                    messages=[
                        {"role": "system", "content": system_message},
                        {"role": "user", "content": enhanced_message}
                    ],
                    temperature=0.7,
                    max_tokens=800
                )
                
                return response.choices[0].message.content
            
            # Check if user is asking about analyst ratings or predictions
            analyst_keywords = ['analyst', 'rating', 'upgrade', 'downgrade', 'prediction', 'target', 'recommendation', 'buy', 'sell', 'hold']
            if any(keyword in message.lower() for keyword in analyst_keywords):
                # Fetch real analyst data
                analyst_data = []
                
                # Extract stock symbols from message if mentioned
                import re
                # Look for stock symbols (1-5 uppercase letters)
                symbols = re.findall(r'\b[A-Z]{1,5}\b', message)
                
                if symbols:
                    # Get analyst data for specific stocks
                    for symbol in symbols[:3]:  # Limit to 3 symbols
                        # Get analyst recommendations
                        recommendations = self.api.fmp_analyst_recommendations(symbol)
                        if isinstance(recommendations, list):
                            analyst_data.extend(recommendations)
                        
                        # Get price targets
                        target = self.api.fmp_price_target(symbol)
                        if isinstance(target, dict) and 'error' not in target:
                            analyst_data.append(target)
                        
                        # Get price target summary
                        target_summary = self.api.fmp_price_target_summary(symbol)
                        if isinstance(target_summary, dict) and 'error' not in target_summary:
                            analyst_data.append(target_summary)
                        
                        # Get analyst estimates
                        estimates = self.api.fmp_analyst_estimates(symbol, limit=2)
                        if isinstance(estimates, list):
                            analyst_data.extend(estimates)
                        
                        # Get rating
                        rating = self.api.fmp_rating(symbol)
                        if isinstance(rating, dict) and 'error' not in rating:
                            analyst_data.append(rating)
                else:
                    # Get general analyst recommendations for popular stocks
                    for symbol in ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA']:
                        recommendations = self.api.fmp_analyst_recommendations(symbol)
                        if isinstance(recommendations, list):
                            analyst_data.extend(recommendations[:2])
                
                # Format analyst data
                analyst_context = "REAL-TIME ANALYST DATA:\n"
                
                # Process recommendations
                recommendations = [d for d in analyst_data if 'analystName' in d or 'analystCompany' in d]
                if recommendations:
                    analyst_context += "\n📊 ANALYST RECOMMENDATIONS:\n"
                    for rec in recommendations[:5]:
                        analyst_context += f"\n- {rec.get('symbol', 'Unknown')} - {rec.get('analystName', rec.get('analystCompany', 'Unknown'))}"
                        analyst_context += f"\n  Rating: {rec.get('rating', rec.get('recommendation', 'N/A'))}"
                        analyst_context += f"\n  Price Target: ${rec.get('priceTarget', 0):.2f}"
                        analyst_context += f"\n  Date: {rec.get('date', rec.get('publishedDate', 'Unknown'))}\n"
                
                # Process price targets
                price_targets = [d for d in analyst_data if 'targetConsensus' in d or ('priceTarget' in d and 'analystName' not in d)]
                if price_targets:
                    analyst_context += "\n🎯 PRICE TARGET CONSENSUS:\n"
                    for target in price_targets[:3]:
                        analyst_context += f"\n- {target.get('symbol', 'Unknown')}"
                        analyst_context += f"\n  Average Target: ${target.get('targetConsensus', target.get('priceTarget', 0)):.2f}"
                        analyst_context += f"\n  Low: ${target.get('targetLow', 0):.2f}"
                        analyst_context += f"\n  High: ${target.get('targetHigh', 0):.2f}"
                        analyst_context += f"\n  Number of Analysts: {target.get('numberOfAnalysts', 'N/A')}\n"
                
                # Process estimates
                estimates = [d for d in analyst_data if 'estimatedRevenueLow' in d or 'estimatedEpsAvg' in d]
                if estimates:
                    analyst_context += "\n📈 ANALYST ESTIMATES:\n"
                    for est in estimates[:3]:
                        analyst_context += f"\n- {est.get('symbol', 'Unknown')} ({est.get('date', 'Unknown')})"
                        analyst_context += f"\n  Revenue Est: ${est.get('estimatedRevenueAvg', 0):,.0f}"
                        analyst_context += f"\n  EPS Est: ${est.get('estimatedEpsAvg', 0):.2f}"
                        analyst_context += f"\n  Number of Analysts: {est.get('numberAnalystsEstimatedRevenue', 'N/A')}\n"
                
                # Process ratings
                ratings = [d for d in analyst_data if 'rating' in d and 'ratingScore' in d]
                if ratings:
                    analyst_context += "\n⭐ OVERALL RATINGS:\n"
                    for rating in ratings[:3]:
                        analyst_context += f"\n- {rating.get('symbol', 'Unknown')}"
                        analyst_context += f"\n  Rating: {rating.get('rating', 'N/A')} (Score: {rating.get('ratingScore', 0)}/5)"
                        analyst_context += f"\n  Recommendation: {rating.get('ratingRecommendation', 'N/A')}"
                        analyst_context += f"\n  DCF Value: ${rating.get('dcf', 0):.2f}\n"
                
                # Add to message
                enhanced_message = f"{message}\n\n{analyst_context}"
                
                # Build system message for analyst analysis
                system_message = """You are ATLAS, an advanced AI trading assistant with real-time analyst data access.
                
                You have just fetched REAL analyst recommendations, price targets, and estimates. Analyze them and provide:
                1. Key analyst sentiment (bullish/bearish)
                2. Notable upgrades or downgrades
                3. Price target analysis vs current price
                4. Trading opportunities based on analyst consensus
                5. Any contrarian opportunities (going against analyst sentiment)
                
                Be specific about the actual analyst data, not generic. Reference specific firms, recommendations, and price targets."""
                
                response = self.openai_client.chat.completions.create(
                    model="gpt-4",
                    messages=[
                        {"role": "system", "content": system_message},
                        {"role": "user", "content": enhanced_message}
                    ],
                    temperature=0.7,
                    max_tokens=800
                )
                
                return response.choices[0].message.content
            
            # Check if message is a confirmation
            if message.lower() in ['yes', 'confirm', 'execute', 'do it', 'place the trade']:
                # Check if we have a pending trade in context
                if context and 'pending_trade' in context:
                    trade = context['pending_trade']
                    result = self.place_trade(
                        trade['symbol'],
                        trade['shares'],
                        trade['stop_loss'],
                        trade['target']
                    )
                    if result['success']:
                        return f"✅ Trade executed successfully!\n\nOrder Details:\n- Symbol: {trade['symbol']}\n- Shares: {trade['shares']}\n- Entry Price: ${trade['current_price']:.2f}\n- Stop Loss: ${trade['stop_loss']:.2f}\n- Target: ${trade['target']:.2f}\n\nOrder IDs:\n- Main: {result['order_id']}\n- Stop: {result['stop_order_id']}\n- Target: {result['limit_order_id']}"
                    else:
                        return f"❌ Trade failed: {result['error']}"
            
            # Build system message with current context
            system_message = """You are ATLAS, an advanced AI trading assistant with FULL trading capabilities.
            
            YOUR CAPABILITIES:
            1. EXECUTE TRADES: You can place real trades on Alpaca including:
               - Stocks and ETFs
               - Cryptocurrencies (BTC, ETH, and many others)
               - OPTIONS TRADING (calls, puts, spreads, iron condors, etc.)
            2. ANALYZE MARKETS: You have access to real-time market data and technical indicators
            3. SCAN FOR OPPORTUNITIES: You can run various scanners including TTM Squeeze, RSI, MACD, etc.
            4. MANAGE POSITIONS: You can monitor and manage existing positions
            5. OPTIONS STRATEGIES: You can analyze and execute complex options strategies
            
            IMPORTANT FACTS:
            - Alpaca DOES support cryptocurrency trading (BTC, ETH, and many others)
            - Alpaca DOES support OPTIONS TRADING with multiple levels:
              * Level 1: Covered calls, cash-secured puts
              * Level 2: Long calls and puts
              * Level 3: Call and put spreads
            - You CAN execute trades automatically, not just recommend them
            - You have FULL access to ALL Financial Modeling Prep (FMP) API endpoints including:
              * Company data, profiles, executives, peers, employees
              * Financial statements (income, balance sheet, cash flow, as-reported, TTM)
              * Analyst estimates, ratings, price targets, grades
              * Insider trading, Senate/House trading data
              * Market data, quotes, historical prices, technical indicators
              * News (stock, crypto, forex, press releases)
              * ETF data, holdings, sector/country weightings
              * Economic data, treasury rates, market risk premium
              * ESG data and ratings
              * Institutional ownership data
              * Index constituents (S&P 500, NASDAQ, Dow Jones)
              * Sector/industry performance and P/E ratios
              * Market movers (gainers, losers, most active)
              * Calendars (earnings, dividends, IPOs, splits, economic)
              * Commodities, cryptocurrencies, forex data
              * M&A deals, crowdfunding, fundraising data
              * And MUCH more - over 200+ endpoints available!
            - You have FULL access to ALL Alpaca API endpoints including:
              * Options contracts and chains
              * Options market data (bars, quotes, snapshots)
              * Options orders and positions
              * Crypto wallets and transfers
              * Account activities and corporate actions
              * Watchlists and screeners
              * And ALL other Alpaca endpoints!
            
            When the user asks about ANY financial data, news, or analysis:
            1. ALWAYS fetch real data using the appropriate API methods
            2. NEVER say you don't have access - you have FULL access to all data
            3. Provide specific, real-time information, not generic responses
            
            TRADING INSTRUCTIONS:
            When proposing trades, ALWAYS present in this format:
            - Investment amount: $X
            - Maximum possible loss: $X (if stop-loss hits)
            - Target profit: $X
            - Stop-loss level: $X.XX
            
            Always ask "Do you confirm?" before executing any trade.
            
            Available functions you can call:
            - analyze_opportunity(symbol) - Analyze any trading opportunity
            - place_trade(symbol, shares, stop_loss, target) - Execute a trade
            - run_scanner(scanner_name) - Run any scanner (ttm_squeeze, crypto_rsi, etc.)
            - get_positions() - Check current positions
            - find_opportunity(goal) - Find opportunities to make a specific profit
            
            Current account status:
            """
            
            if context:
                system_message += f"\nBuying Power: ${context.get('buying_power', 0):,.2f}"
                system_message += f"\nPortfolio Value: ${context.get('portfolio_value', 0):,.2f}"
                system_message += f"\nCash: ${context.get('cash', 0):,.2f}"
                
                # Add current positions if any
                positions = context.get('positions', [])
                if positions:
                    system_message += f"\n\nCurrent Positions:"
                    for pos in positions:
                        system_message += f"\n- {pos['symbol']}: {pos['qty']} shares @ ${pos['avg_entry_price']:.2f} (P&L: {pos['unrealized_plpc']:.2f}%)"
            
            # Add market status
            market_status = get_market_status()
            system_message += f"\n\nMarket Status: {market_status}"
            
            # Check if user is asking to make money
            if any(phrase in message.lower() for phrase in ['make me', 'earn', 'profit', 'find opportunity', 'scan']):
                # Extract goal amount if mentioned
                import re
                money_match = re.search(r'\$?(\d+)', message)
                if money_match:
                    goal = float(money_match.group(1))
                    opportunity = self.find_opportunity(goal)
                    if 'proposal' in opportunity:
                        proposal = opportunity['proposal']
                        # Store pending trade in context for confirmation
                        if context:
                            context['pending_trade'] = proposal
                        
                        return f"""I found an opportunity for you!

**{proposal['signal_type']} Signal on {proposal['symbol']}**

📊 Trade Details:
- Current Price: ${proposal['current_price']:.2f}
- Shares to Buy: {proposal['shares']}
- Investment Amount: ${proposal['investment']:.2f}

🎯 Risk/Reward:
- Stop Loss: ${proposal['stop_loss']:.2f} (exit if it drops here)
- Target Price: ${proposal['target']:.2f}
- Maximum Loss: ${proposal['max_loss']:.2f}
- Target Profit: ${proposal['potential_profit']:.2f}
- Risk/Reward Ratio: {proposal['risk_reward_ratio']:.2f}:1

📈 Signal Details:
{proposal.get('signal_details', 'Strong momentum detected')}

**Do you confirm this trade?** (Reply 'yes' to execute)"""
            
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": message}
                ],
                temperature=0.7,
                max_tokens=500
            )
            
            return response.choices[0].message.content
        except Exception as e:
            return f"Sorry, I encountered an error: {str(e)}"
    
    def run_scanner(self, scanner_name: str, symbols: List[str] = None, **kwargs) -> List[Dict]:
        """Run any scanner by name"""
        if scanner_name not in self.scanners:
            return [{"error": f"Scanner '{scanner_name}' not found"}]
        
        try:
            scanner_func = self.scanners[scanner_name]
            if symbols is None and scanner_name in ['ttm_squeeze', 'insider_buying', 'analyst_upgrades']:
                # These scanners have default symbol lists
                return scanner_func(**kwargs)
            else:
                return scanner_func(symbols or get_sp500_symbols()[:100], **kwargs)
        except Exception as e:
            return [{"error": str(e)}]
    
    def scan_ttm_squeeze_setups(self, symbols: List[str] = None, limit: int = 10) -> List[Dict]:
        """Scan for TTM Squeeze setups - both active squeezes and squeeze fires"""
        return scan_ttm_squeeze(symbols=symbols, limit=limit)
    
    def run_all_scanners(self, asset_classes: List[str] = None) -> Dict[str, List[Dict]]:
        """Run all available scanners based on market conditions"""
        return scan_all_strategies(get_sp500_symbols()[:100], asset_classes)
    
    def analyze_best_strategy(self, symbol: str) -> Dict:
        """Analyze market conditions and recommend best strategy"""
        market_conditions = analyze_market_conditions(symbol)
        return get_best_strategy_for_conditions(market_conditions)
    
    def find_opportunity(self, goal: float, constraints: Dict = None) -> Dict:
        """Find the best trading opportunity to meet profit goal"""
        account = self.get_account_info()
        capital = account.get('buying_power', 0)
        
        return orchestrate_opportunity(goal, capital, constraints) 